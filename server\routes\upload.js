const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

const { catchAsync, FileUploadError, ValidationError, NotFoundError } = require('../middleware/errorHandler');
const { customLogger } = require('../middleware/logger');
const Upload = require('../models/Upload');

const router = express.Router();

// Configure multer for file upload
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, '..', process.env.UPLOAD_DIR || 'uploads');
        
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        // Generate unique filename
        const uniqueSuffix = uuidv4();
        const extension = path.extname(file.originalname);
        const filename = `chart_${uniqueSuffix}${extension}`;
        cb(null, filename);
    }
});

// File filter
const fileFilter = (req, file, cb) => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new FileUploadError('Only image files are allowed (JPEG, PNG, GIF, WebP)'), false);
    }
};

// Configure multer
const upload = multer({
    storage,
    fileFilter,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
        files: 1 // Only one file at a time
    }
});

// Middleware to handle multer errors
const handleMulterError = (err, req, res, next) => {
    if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
            return next(new FileUploadError('File too large. Maximum size is 10MB'));
        }
        if (err.code === 'LIMIT_FILE_COUNT') {
            return next(new FileUploadError('Too many files. Only one file allowed'));
        }
        if (err.code === 'LIMIT_UNEXPECTED_FILE') {
            return next(new FileUploadError('Unexpected field name. Use "image" field'));
        }
        return next(new FileUploadError(`Upload error: ${err.message}`));
    }
    next(err);
};

// Validate uploaded file
const validateFile = (req, res, next) => {
    if (!req.file) {
        return next(new ValidationError('No file uploaded. Please select an image file'));
    }
    
    // Additional validation
    const { file } = req;
    const maxSize = parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024;
    
    if (file.size > maxSize) {
        // Clean up uploaded file
        fs.unlink(file.path, () => {});
        return next(new FileUploadError('File too large. Maximum size is 10MB'));
    }
    
    // Check if file actually exists
    if (!fs.existsSync(file.path)) {
        return next(new FileUploadError('File upload failed. Please try again'));
    }
    
    customLogger.info(`File uploaded successfully: ${file.filename}`, {
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype
    });
    
    next();
};

// POST /api/upload - Upload image file
router.post('/', 
    upload.single('image'),
    handleMulterError,
    validateFile,
    catchAsync(async (req, res) => {
        const { file } = req;
        
        try {
            // Save upload record to database
            const uploadResult = await Upload.create(file);
            
            if (!uploadResult.success) {
                // Clean up file if database save fails
                fs.unlink(file.path, () => {});
                throw new Error(uploadResult.error);
            }
            
            const uploadId = uploadResult.data.insertId;
            
            customLogger.success(`Upload record created with ID: ${uploadId}`);
            
            // Return success response
            res.status(201).json({
                success: true,
                message: 'File uploaded successfully',
                data: {
                    id: uploadId,
                    uploadId: uploadId,
                    filename: file.filename,
                    originalName: file.originalname,
                    size: file.size,
                    mimetype: file.mimetype,
                    uploadDate: new Date().toISOString(),
                    status: 'uploaded'
                }
            });
            
        } catch (error) {
            // Clean up file if anything goes wrong
            fs.unlink(file.path, () => {});
            customLogger.error('Failed to save upload record', { error: error.message });
            throw new FileUploadError('Failed to save upload record');
        }
    })
);

// GET /api/upload/:id - Get upload details
router.get('/:id', catchAsync(async (req, res) => {
    const { id } = req.params;
    
    if (!id || isNaN(id)) {
        throw new ValidationError('Invalid upload ID');
    }
    
    const result = await Upload.findById(parseInt(id));
    
    if (!result.success) {
        throw new Error(result.error);
    }
    
    if (!result.data) {
        throw new NotFoundError('Upload not found');
    }
    
    res.json({
        success: true,
        data: result.data
    });
}));

// GET /api/upload - Get all uploads with pagination
router.get('/', catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    if (page < 1 || limit < 1 || limit > 100) {
        throw new ValidationError('Invalid pagination parameters');
    }
    
    const [uploadsResult, countResult] = await Promise.all([
        Upload.getAll(page, limit),
        Upload.getCount()
    ]);
    
    if (!uploadsResult.success || !countResult.success) {
        throw new Error('Failed to fetch uploads');
    }
    
    const totalItems = countResult.data.total;
    const totalPages = Math.ceil(totalItems / limit);
    
    res.json({
        success: true,
        data: {
            uploads: uploadsResult.data,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems,
                itemsPerPage: limit,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            }
        }
    });
}));

// DELETE /api/upload/:id - Delete upload and file
router.delete('/:id', catchAsync(async (req, res) => {
    const { id } = req.params;
    
    if (!id || isNaN(id)) {
        throw new ValidationError('Invalid upload ID');
    }
    
    // Get upload details first
    const uploadResult = await Upload.findById(parseInt(id));
    
    if (!uploadResult.success) {
        throw new Error(uploadResult.error);
    }
    
    if (!uploadResult.data) {
        throw new NotFoundError('Upload not found');
    }
    
    const upload = uploadResult.data;
    
    // Delete from database
    const deleteResult = await Upload.delete(parseInt(id));
    
    if (!deleteResult.success) {
        throw new Error(deleteResult.error);
    }
    
    // Delete physical file
    if (fs.existsSync(upload.filepath)) {
        fs.unlink(upload.filepath, (err) => {
            if (err) {
                customLogger.warn(`Failed to delete file: ${upload.filepath}`, { error: err.message });
            } else {
                customLogger.info(`File deleted: ${upload.filepath}`);
            }
        });
    }
    
    customLogger.success(`Upload deleted: ${id}`);
    
    res.json({
        success: true,
        message: 'Upload deleted successfully'
    });
}));

module.exports = router;
