#!/usr/bin/env python3
"""
Contoh penggunaan Market Trend Analysis Engine yang telah ditingkatkan
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import MarketAnalysisEngine

def demonstrate_enhanced_features():
    """Demonstrasi fitur-fitur yang telah ditingkatkan"""
    
    print("🚀 Market Trend Analysis Engine - Enhanced Version")
    print("=" * 60)
    
    # Initialize the enhanced engine
    print("📊 Initializing Enhanced Market Analysis Engine...")
    engine = MarketAnalysisEngine()
    print("✅ Engine initialized with enhanced features:")
    print("   • Enhanced Decision Engine")
    print("   • Advanced Signal Analyzer") 
    print("   • Pattern Recognition")
    print("   • Multi-timeframe Analysis")
    print("   • Signal Reasoning System")
    print()
    
    # Simulate analysis (since we don't have actual chart image)
    print("🔍 Simulating Enhanced Analysis...")
    print("Note: <PERSON><PERSON> pengg<PERSON> n<PERSON>, gunakan: engine.analyze_image('path/to/chart.png')")
    print()
    
    # Create a mock result to demonstrate the enhanced output format
    mock_result = create_mock_enhanced_result()
    
    # Display enhanced analysis results
    display_enhanced_results(mock_result)

def create_mock_enhanced_result():
    """Create mock result to demonstrate enhanced features"""
    return {
        "success": True,
        "upload_id": "demo_analysis_001",
        "timestamp": datetime.now().isoformat(),
        "ocr_data": {
            "extracted_text": "EURUSD H1 Chart",
            "symbol": "EURUSD",
            "timeframe": "H1",
            "current_price": 1.2050,
            "price_data": {
                "high": 1.2080,
                "low": 1.2020,
                "open": 1.2030,
                "close": 1.2050
            }
        },
        "analysis": {
            "recommendation": "BUY",
            "confidence_score": 0.82,
            "confidence_level": "high",
            "confidence_description": "Strong confluence signals with excellent timeframe alignment",
            "entry_price": 1.2055,
            "stop_loss": 1.2020,
            "take_profit": 1.2120,
            "risk_reward_ratio": 1.86,
            "position_size": 0.02,
            "market_regime": "trending_bull",
            
            # Enhanced reasoning
            "signal_reasoning": {
                "primary_signals": [
                    "Bullish price action pattern detected",
                    "Smart Money Concept shows institutional buying",
                    "Technical indicators align bullish"
                ],
                "supporting_factors": [
                    "Multiple timeframe alignment",
                    "Strong confluence score (0.85)",
                    "Low market noise conditions"
                ],
                "risk_factors": [
                    "Approaching resistance level",
                    "Moderate volatility environment"
                ]
            },
            
            # Advanced patterns
            "advanced_patterns": [
                {
                    "pattern": "double_bottom",
                    "confidence": 0.78,
                    "signal_type": "BUY",
                    "description": "Double bottom pattern with strong reversal potential",
                    "key_levels": {
                        "neckline": 1.2070,
                        "support": 1.2020,
                        "target": 1.2120
                    }
                },
                {
                    "pattern": "flag_bullish",
                    "confidence": 0.65,
                    "signal_type": "BUY", 
                    "description": "Bullish flag pattern after strong upward move"
                }
            ],
            
            # Multi-timeframe analysis
            "multi_timeframe_analysis": {
                "current_timeframe": "H1",
                "alignment_score": 0.85,
                "trading_bias": "BULLISH",
                "confidence_multiplier": 1.2,
                "alignment_description": "Excellent alignment across timeframes - high probability setup"
            },
            
            # Signal quality metrics
            "signal_quality_metrics": {
                "average_quality": 0.78,
                "total_signals": 5,
                "quality_distribution": {
                    "high": 3,
                    "medium": 2,
                    "low": 0
                }
            },
            
            # Analysis score
            "analysis_score": {
                "overall_score": 0.82,
                "grade": "A",
                "description": "Excellent - Setup trading berkualitas tinggi",
                "component_scores": {
                    "signal_quality": 0.78,
                    "pattern_quality": 0.72,
                    "mtf_alignment": 0.85
                }
            },
            
            # Risk assessment
            "risk_assessment": {
                "risk_level": "medium",
                "risk_factors": [
                    "Approaching resistance level"
                ],
                "risk_score": 0.4,
                "mitigation_suggestions": [
                    "Use tighter stop losses",
                    "Consider partial profit taking at resistance"
                ]
            },
            
            # Execution timing
            "execution_timing": {
                "timing": "immediate",
                "reason": "Strong signals with excellent timeframe alignment",
                "urgency": "high"
            },
            
            # Metadata
            "analysis_version": "enhanced_v2.0",
            "enhanced_features": [
                "Advanced pattern recognition",
                "Multi-timeframe analysis simulation",
                "Signal confluence analysis", 
                "Enhanced risk management",
                "Detailed signal reasoning",
                "Market regime detection"
            ]
        }
    }

def display_enhanced_results(result):
    """Display enhanced analysis results in a user-friendly format"""
    
    analysis = result["analysis"]
    
    print("📈 ENHANCED ANALYSIS RESULTS")
    print("=" * 60)
    
    # Basic recommendation
    print(f"🎯 RECOMMENDATION: {analysis['recommendation']}")
    print(f"📊 CONFIDENCE: {analysis['confidence_score']:.2f} ({analysis['confidence_level']})")
    print(f"🏛️ MARKET REGIME: {analysis['market_regime']}")
    print(f"⭐ ANALYSIS GRADE: {analysis['analysis_score']['grade']}")
    print()
    
    # Trading details
    print("💰 TRADING DETAILS:")
    print(f"   Entry Price: {analysis['entry_price']}")
    print(f"   Stop Loss: {analysis['stop_loss']}")
    print(f"   Take Profit: {analysis['take_profit']}")
    print(f"   Risk/Reward: {analysis['risk_reward_ratio']:.2f}")
    print(f"   Position Size: {analysis['position_size']}")
    print()
    
    # Signal reasoning (NEW FEATURE)
    print("🧠 SIGNAL REASONING:")
    reasoning = analysis['signal_reasoning']
    print("   Primary Signals:")
    for signal in reasoning['primary_signals']:
        print(f"     • {signal}")
    print("   Supporting Factors:")
    for factor in reasoning['supporting_factors']:
        print(f"     • {factor}")
    if reasoning['risk_factors']:
        print("   Risk Factors:")
        for risk in reasoning['risk_factors']:
            print(f"     • {risk}")
    print()
    
    # Advanced patterns (NEW FEATURE)
    print("🔍 ADVANCED PATTERNS DETECTED:")
    for pattern in analysis['advanced_patterns']:
        print(f"   • {pattern['pattern'].replace('_', ' ').title()}")
        print(f"     Confidence: {pattern['confidence']:.2f}")
        print(f"     Signal: {pattern['signal_type']}")
        print(f"     Description: {pattern['description']}")
    print()
    
    # Multi-timeframe analysis (NEW FEATURE)
    print("⏰ MULTI-TIMEFRAME ANALYSIS:")
    mtf = analysis['multi_timeframe_analysis']
    print(f"   Current Timeframe: {mtf['current_timeframe']}")
    print(f"   Alignment Score: {mtf['alignment_score']:.2f}")
    print(f"   Trading Bias: {mtf['trading_bias']}")
    print(f"   Description: {mtf['alignment_description']}")
    print()
    
    # Signal quality metrics (NEW FEATURE)
    print("📊 SIGNAL QUALITY METRICS:")
    quality = analysis['signal_quality_metrics']
    print(f"   Average Quality: {quality['average_quality']:.2f}")
    print(f"   Total Signals: {quality['total_signals']}")
    print(f"   Quality Distribution: High({quality['quality_distribution']['high']}) "
          f"Medium({quality['quality_distribution']['medium']}) "
          f"Low({quality['quality_distribution']['low']})")
    print()
    
    # Risk assessment (NEW FEATURE)
    print("⚠️ RISK ASSESSMENT:")
    risk = analysis['risk_assessment']
    print(f"   Risk Level: {risk['risk_level']}")
    print(f"   Risk Score: {risk['risk_score']:.2f}")
    if risk['risk_factors']:
        print("   Risk Factors:")
        for factor in risk['risk_factors']:
            print(f"     • {factor}")
    if risk['mitigation_suggestions']:
        print("   Mitigation Suggestions:")
        for suggestion in risk['mitigation_suggestions']:
            print(f"     • {suggestion}")
    print()
    
    # Execution timing (NEW FEATURE)
    print("⏱️ EXECUTION TIMING:")
    timing = analysis['execution_timing']
    print(f"   Timing: {timing['timing']}")
    print(f"   Urgency: {timing['urgency']}")
    print(f"   Reason: {timing['reason']}")
    print()
    
    print("✨ ENHANCED FEATURES DEMONSTRATED:")
    for feature in analysis['enhanced_features']:
        print(f"   ✅ {feature}")
    
    print("\n" + "=" * 60)
    print("🎉 Analysis completed with enhanced features!")

def show_usage_examples():
    """Show practical usage examples"""
    
    print("\n📚 PRACTICAL USAGE EXAMPLES:")
    print("=" * 60)
    
    print("1. Basic Analysis:")
    print("   from main import MarketAnalysisEngine")
    print("   engine = MarketAnalysisEngine()")
    print("   result = engine.analyze_image('chart.png')")
    print()
    
    print("2. Access Enhanced Features:")
    print("   # Get signal reasoning")
    print("   reasoning = result['analysis']['signal_reasoning']")
    print("   print(reasoning['primary_signals'])")
    print()
    print("   # Get advanced patterns")
    print("   patterns = result['analysis']['advanced_patterns']")
    print("   for pattern in patterns:")
    print("       print(f\"Pattern: {pattern['pattern']}\")")
    print()
    print("   # Get multi-timeframe analysis")
    print("   mtf = result['analysis']['multi_timeframe_analysis']")
    print("   print(f\"Alignment: {mtf['alignment_score']}\")")
    print()
    
    print("3. Risk Management:")
    print("   risk = result['analysis']['risk_assessment']")
    print("   if risk['risk_level'] == 'high':")
    print("       print('High risk detected, consider smaller position')")
    print()
    
    print("4. Quality Check:")
    print("   score = result['analysis']['analysis_score']")
    print("   if score['grade'] in ['A', 'B']:")
    print("       print('High quality setup detected')")

if __name__ == "__main__":
    demonstrate_enhanced_features()
    show_usage_examples()
