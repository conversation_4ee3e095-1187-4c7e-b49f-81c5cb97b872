"""
Advanced Signal Analyzer Module
Provides enhanced signal analysis with multi-timeframe analysis, pattern recognition,
and advanced filtering techniques for improved accuracy
"""

import cv2
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class AdvancedSignalAnalyzer:
    """Advanced signal analyzer with enhanced pattern recognition and filtering"""
    
    def __init__(self):
        # Enhanced pattern recognition parameters
        self.pattern_confidence_thresholds = {
            'very_high': 0.85,
            'high': 0.70,
            'medium': 0.55,
            'low': 0.40
        }
        
        # Multi-timeframe analysis weights
        self.timeframe_weights = {
            'M1': 0.1,   # Low weight for noise
            'M5': 0.3,   # Medium weight
            'M15': 0.5,  # Good balance
            'M30': 0.7,  # Higher weight
            'H1': 0.8,   # High weight
            'H4': 0.9,   # Very high weight
            'D1': 1.0    # Maximum weight
        }
        
        # Signal filtering criteria
        self.filtering_criteria = {
            'min_pattern_strength': 0.4,
            'min_volume_confirmation': 0.3,
            'max_noise_ratio': 0.6,
            'confluence_requirement': 2,  # Minimum signals for confluence
            'consistency_threshold': 0.7
        }
        
        # Advanced pattern types with enhanced detection
        self.advanced_patterns = {
            'double_top': {'reversal': True, 'strength': 0.8, 'reliability': 0.75},
            'double_bottom': {'reversal': True, 'strength': 0.8, 'reliability': 0.75},
            'head_shoulders': {'reversal': True, 'strength': 0.9, 'reliability': 0.85},
            'inverse_head_shoulders': {'reversal': True, 'strength': 0.9, 'reliability': 0.85},
            'ascending_triangle': {'reversal': False, 'strength': 0.7, 'reliability': 0.70},
            'descending_triangle': {'reversal': False, 'strength': 0.7, 'reliability': 0.70},
            'symmetrical_triangle': {'reversal': False, 'strength': 0.6, 'reliability': 0.65},
            'flag_pattern': {'reversal': False, 'strength': 0.75, 'reliability': 0.70},
            'pennant_pattern': {'reversal': False, 'strength': 0.75, 'reliability': 0.70},
            'wedge_rising': {'reversal': True, 'strength': 0.8, 'reliability': 0.75},
            'wedge_falling': {'reversal': True, 'strength': 0.8, 'reliability': 0.75}
        }
    
    def analyze_advanced_signals(self, image: np.ndarray, price_action: Dict, 
                                smc: Dict, technical: Dict, ocr_data: Dict) -> Dict[str, Any]:
        """
        Main advanced signal analysis function
        
        Args:
            image: Processed chart image
            price_action: Price action analysis results
            smc: SMC analysis results
            technical: Technical indicators results
            ocr_data: OCR extracted data
            
        Returns:
            Dict containing advanced signal analysis results
        """
        try:
            logger.info("Starting advanced signal analysis...")
            
            # Step 1: Enhanced pattern recognition
            advanced_patterns = self._detect_advanced_patterns(image, price_action)
            
            # Step 2: Multi-timeframe analysis simulation
            mtf_analysis = self._simulate_multi_timeframe_analysis(ocr_data, price_action, technical)
            
            # Step 3: Signal confluence analysis
            confluence_signals = self._analyze_signal_confluence_advanced(
                price_action, smc, technical, advanced_patterns
            )
            
            # Step 4: Noise filtering and signal validation
            filtered_signals = self._apply_advanced_filtering(confluence_signals, image)
            
            # Step 5: Signal strength enhancement
            enhanced_signals = self._enhance_signal_strength(filtered_signals, mtf_analysis)
            
            # Step 6: Generate signal explanations
            signal_explanations = self._generate_advanced_explanations(enhanced_signals, advanced_patterns)
            
            # Step 7: Calculate overall analysis score
            analysis_score = self._calculate_advanced_analysis_score(
                enhanced_signals, advanced_patterns, mtf_analysis
            )
            
            result = {
                'advanced_patterns': advanced_patterns,
                'multi_timeframe_analysis': mtf_analysis,
                'confluence_signals': confluence_signals,
                'filtered_signals': filtered_signals,
                'enhanced_signals': enhanced_signals,
                'signal_explanations': signal_explanations,
                'analysis_score': analysis_score,
                'signal_quality_metrics': self._calculate_signal_quality_metrics(enhanced_signals),
                'recommendation_confidence': self._calculate_recommendation_confidence(enhanced_signals, mtf_analysis),
                'risk_assessment': self._assess_advanced_risks(enhanced_signals, advanced_patterns),
                'execution_timing': self._determine_optimal_timing(enhanced_signals, mtf_analysis),
                'analysis_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'version': 'advanced_v1.0',
                    'total_patterns_detected': len(advanced_patterns),
                    'signals_after_filtering': len(filtered_signals),
                    'final_enhanced_signals': len(enhanced_signals)
                }
            }
            
            logger.info(f"Advanced signal analysis completed. Found {len(enhanced_signals)} high-quality signals")
            return result
            
        except Exception as e:
            logger.error(f"Advanced signal analysis failed: {str(e)}")
            return self._generate_default_advanced_analysis()
    
    def _detect_advanced_patterns(self, image: np.ndarray, price_action: Dict) -> List[Dict[str, Any]]:
        """
        Detect advanced chart patterns using enhanced algorithms
        """
        try:
            patterns = []
            
            # Get price data points from image analysis
            price_points = self._extract_price_points_from_image(image)
            
            if len(price_points) < 10:  # Need minimum points for pattern detection
                logger.warning("Insufficient price points for advanced pattern detection")
                return patterns
            
            # Detect each pattern type
            for pattern_name, pattern_config in self.advanced_patterns.items():
                detected_pattern = self._detect_specific_pattern(
                    price_points, pattern_name, pattern_config
                )
                
                if detected_pattern:
                    patterns.append(detected_pattern)
            
            # Sort patterns by confidence and strength
            patterns.sort(key=lambda p: p.get('confidence', 0) * p.get('strength', 0), reverse=True)
            
            return patterns[:5]  # Return top 5 patterns
            
        except Exception as e:
            logger.error(f"Advanced pattern detection failed: {str(e)}")
            return []
    
    def _extract_price_points_from_image(self, image: np.ndarray) -> List[Dict[str, float]]:
        """
        Extract price points from chart image using advanced computer vision
        """
        try:
            # Convert to grayscale for processing
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply edge detection to find price line
            edges = cv2.Canny(gray, 50, 150)
            
            # Find contours that might represent price movement
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            price_points = []
            
            # Process contours to extract price points
            for contour in contours:
                if cv2.contourArea(contour) > 100:  # Filter small noise
                    # Approximate contour to get key points
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    
                    for point in approx:
                        x, y = point[0]
                        price_points.append({
                            'x': float(x),
                            'y': float(y),
                            'timestamp': x,  # X represents time
                            'price': image.shape[0] - y  # Invert Y for price (higher Y = lower price)
                        })
            
            # Sort by timestamp and remove duplicates
            price_points.sort(key=lambda p: p['timestamp'])
            
            # Remove points that are too close together
            filtered_points = []
            for point in price_points:
                if not filtered_points or abs(point['timestamp'] - filtered_points[-1]['timestamp']) > 10:
                    filtered_points.append(point)
            
            return filtered_points[:50]  # Limit to 50 points for performance
            
        except Exception as e:
            logger.error(f"Price point extraction failed: {str(e)}")
            return []
    
    def _detect_specific_pattern(self, price_points: List[Dict], pattern_name: str, 
                                pattern_config: Dict) -> Optional[Dict[str, Any]]:
        """
        Detect specific chart pattern from price points
        """
        try:
            if len(price_points) < 5:
                return None
            
            # Extract price values for analysis
            prices = [p['price'] for p in price_points]
            timestamps = [p['timestamp'] for p in price_points]
            
            # Pattern-specific detection logic
            if pattern_name == 'double_top':
                return self._detect_double_top(prices, timestamps, pattern_config)
            elif pattern_name == 'double_bottom':
                return self._detect_double_bottom(prices, timestamps, pattern_config)
            elif pattern_name == 'head_shoulders':
                return self._detect_head_shoulders(prices, timestamps, pattern_config)
            elif pattern_name in ['ascending_triangle', 'descending_triangle', 'symmetrical_triangle']:
                return self._detect_triangle_pattern(prices, timestamps, pattern_name, pattern_config)
            elif pattern_name in ['flag_pattern', 'pennant_pattern']:
                return self._detect_continuation_pattern(prices, timestamps, pattern_name, pattern_config)
            elif pattern_name in ['wedge_rising', 'wedge_falling']:
                return self._detect_wedge_pattern(prices, timestamps, pattern_name, pattern_config)
            
            return None
            
        except Exception as e:
            logger.error(f"Specific pattern detection failed for {pattern_name}: {str(e)}")
            return None
    
    def _detect_double_top(self, prices: List[float], timestamps: List[float], 
                          config: Dict) -> Optional[Dict[str, Any]]:
        """Detect double top pattern"""
        try:
            if len(prices) < 10:
                return None
            
            # Find local maxima
            peaks = []
            for i in range(1, len(prices) - 1):
                if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                    peaks.append((i, prices[i]))
            
            if len(peaks) < 2:
                return None
            
            # Look for two peaks at similar levels
            for i in range(len(peaks) - 1):
                for j in range(i + 1, len(peaks)):
                    peak1_idx, peak1_price = peaks[i]
                    peak2_idx, peak2_price = peaks[j]
                    
                    # Check if peaks are at similar levels (within 2%)
                    price_diff = abs(peak1_price - peak2_price) / max(peak1_price, peak2_price)
                    
                    if price_diff < 0.02 and (peak2_idx - peak1_idx) > 5:  # Sufficient separation
                        # Find the valley between peaks
                        valley_start = peak1_idx
                        valley_end = peak2_idx
                        valley_price = min(prices[valley_start:valley_end])
                        
                        # Calculate pattern strength
                        peak_height = max(peak1_price, peak2_price)
                        valley_depth = peak_height - valley_price
                        strength = min(valley_depth / peak_height, 1.0)
                        
                        if strength > 0.03:  # Minimum 3% retracement
                            return {
                                'pattern': 'double_top',
                                'confidence': min(strength * 2, 1.0),
                                'strength': config['strength'],
                                'reliability': config['reliability'],
                                'reversal': config['reversal'],
                                'key_levels': {
                                    'resistance': peak_height,
                                    'support': valley_price,
                                    'neckline': valley_price
                                },
                                'signal_type': 'SELL',
                                'description': f"Double top pattern detected at {peak_height:.5f} level",
                                'pattern_points': {
                                    'peak1': {'timestamp': timestamps[peak1_idx], 'price': peak1_price},
                                    'peak2': {'timestamp': timestamps[peak2_idx], 'price': peak2_price},
                                    'valley': {'price': valley_price}
                                }
                            }
            
            return None

        except Exception as e:
            logger.error(f"Double top detection failed: {str(e)}")
            return None

    def _detect_double_bottom(self, prices: List[float], timestamps: List[float],
                             config: Dict) -> Optional[Dict[str, Any]]:
        """Detect double bottom pattern"""
        try:
            if len(prices) < 10:
                return None

            # Find local minima
            troughs = []
            for i in range(1, len(prices) - 1):
                if prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                    troughs.append((i, prices[i]))

            if len(troughs) < 2:
                return None

            # Look for two troughs at similar levels
            for i in range(len(troughs) - 1):
                for j in range(i + 1, len(troughs)):
                    trough1_idx, trough1_price = troughs[i]
                    trough2_idx, trough2_price = troughs[j]

                    # Check if troughs are at similar levels (within 2%)
                    price_diff = abs(trough1_price - trough2_price) / max(trough1_price, trough2_price)

                    if price_diff < 0.02 and (trough2_idx - trough1_idx) > 5:
                        # Find the peak between troughs
                        peak_start = trough1_idx
                        peak_end = trough2_idx
                        peak_price = max(prices[peak_start:peak_end])

                        # Calculate pattern strength
                        trough_level = min(trough1_price, trough2_price)
                        peak_height = peak_price - trough_level
                        strength = min(peak_height / trough_level, 1.0)

                        if strength > 0.03:  # Minimum 3% rally
                            return {
                                'pattern': 'double_bottom',
                                'confidence': min(strength * 2, 1.0),
                                'strength': config['strength'],
                                'reliability': config['reliability'],
                                'reversal': config['reversal'],
                                'key_levels': {
                                    'support': trough_level,
                                    'resistance': peak_price,
                                    'neckline': peak_price
                                },
                                'signal_type': 'BUY',
                                'description': f"Double bottom pattern detected at {trough_level:.5f} level",
                                'pattern_points': {
                                    'trough1': {'timestamp': timestamps[trough1_idx], 'price': trough1_price},
                                    'trough2': {'timestamp': timestamps[trough2_idx], 'price': trough2_price},
                                    'peak': {'price': peak_price}
                                }
                            }

            return None

        except Exception as e:
            logger.error(f"Double bottom detection failed: {str(e)}")
            return None

    def _detect_head_shoulders(self, prices: List[float], timestamps: List[float],
                              config: Dict) -> Optional[Dict[str, Any]]:
        """Detect head and shoulders pattern"""
        try:
            if len(prices) < 15:
                return None

            # Find local maxima for potential head and shoulders
            peaks = []
            for i in range(2, len(prices) - 2):
                if (prices[i] > prices[i-1] and prices[i] > prices[i+1] and
                    prices[i] > prices[i-2] and prices[i] > prices[i+2]):
                    peaks.append((i, prices[i]))

            if len(peaks) < 3:
                return None

            # Look for head and shoulders pattern (left shoulder, head, right shoulder)
            for i in range(len(peaks) - 2):
                left_shoulder_idx, left_shoulder = peaks[i]
                head_idx, head = peaks[i + 1]
                right_shoulder_idx, right_shoulder = peaks[i + 2]

                # Head should be higher than both shoulders
                if head > left_shoulder and head > right_shoulder:
                    # Shoulders should be roughly equal (within 5%)
                    shoulder_diff = abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder)

                    if shoulder_diff < 0.05:
                        # Find neckline (lowest point between shoulders)
                        left_valley = min(prices[left_shoulder_idx:head_idx])
                        right_valley = min(prices[head_idx:right_shoulder_idx])
                        neckline = max(left_valley, right_valley)  # Conservative neckline

                        # Calculate pattern strength
                        head_height = head - neckline
                        strength = min(head_height / head, 1.0)

                        if strength > 0.05:  # Minimum 5% pattern height
                            return {
                                'pattern': 'head_shoulders',
                                'confidence': min(strength * 1.5, 1.0),
                                'strength': config['strength'],
                                'reliability': config['reliability'],
                                'reversal': config['reversal'],
                                'key_levels': {
                                    'neckline': neckline,
                                    'head_level': head,
                                    'shoulder_level': (left_shoulder + right_shoulder) / 2
                                },
                                'signal_type': 'SELL',
                                'description': f"Head and shoulders pattern with neckline at {neckline:.5f}",
                                'pattern_points': {
                                    'left_shoulder': {'timestamp': timestamps[left_shoulder_idx], 'price': left_shoulder},
                                    'head': {'timestamp': timestamps[head_idx], 'price': head},
                                    'right_shoulder': {'timestamp': timestamps[right_shoulder_idx], 'price': right_shoulder},
                                    'neckline': {'price': neckline}
                                }
                            }

            return None

        except Exception as e:
            logger.error(f"Head and shoulders detection failed: {str(e)}")
            return None

    def _detect_triangle_pattern(self, prices: List[float], timestamps: List[float],
                                pattern_name: str, config: Dict) -> Optional[Dict[str, Any]]:
        """Detect triangle patterns (ascending, descending, symmetrical)"""
        try:
            if len(prices) < 20:
                return None

            # Find highs and lows
            highs = []
            lows = []

            for i in range(2, len(prices) - 2):
                if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                    highs.append((i, prices[i]))
                elif prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                    lows.append((i, prices[i]))

            if len(highs) < 3 or len(lows) < 3:
                return None

            # Calculate trend lines for highs and lows
            high_trend = self._calculate_trend_line([h[1] for h in highs[-3:]])
            low_trend = self._calculate_trend_line([l[1] for l in lows[-3:]])

            if high_trend is None or low_trend is None:
                return None

            high_slope, _ = high_trend
            low_slope, _ = low_trend

            # Determine triangle type based on slopes
            triangle_type = None
            signal_type = 'NEUTRAL'

            if abs(high_slope) < 0.001 and low_slope > 0.001:  # Horizontal resistance, rising support
                triangle_type = 'ascending_triangle'
                signal_type = 'BUY'
            elif high_slope < -0.001 and abs(low_slope) < 0.001:  # Falling resistance, horizontal support
                triangle_type = 'descending_triangle'
                signal_type = 'SELL'
            elif high_slope < -0.001 and low_slope > 0.001:  # Converging lines
                triangle_type = 'symmetrical_triangle'
                signal_type = 'NEUTRAL'  # Direction depends on breakout

            if triangle_type == pattern_name:
                # Calculate convergence point and pattern strength
                latest_high = highs[-1][1]
                latest_low = lows[-1][1]
                pattern_height = latest_high - latest_low

                strength = min(pattern_height / latest_high, 1.0)

                if strength > 0.02:  # Minimum 2% pattern height
                    return {
                        'pattern': triangle_type,
                        'confidence': min(strength * 3, 1.0),
                        'strength': config['strength'],
                        'reliability': config['reliability'],
                        'reversal': config['reversal'],
                        'key_levels': {
                            'upper_trendline': latest_high,
                            'lower_trendline': latest_low,
                            'apex_area': (latest_high + latest_low) / 2
                        },
                        'signal_type': signal_type,
                        'description': f"{triangle_type.replace('_', ' ').title()} pattern detected",
                        'pattern_points': {
                            'recent_highs': [{'timestamp': timestamps[h[0]], 'price': h[1]} for h in highs[-3:]],
                            'recent_lows': [{'timestamp': timestamps[l[0]], 'price': l[1]} for l in lows[-3:]]
                        }
                    }

            return None

        except Exception as e:
            logger.error(f"Triangle pattern detection failed: {str(e)}")
            return None

    def _calculate_trend_line(self, values: List[float]) -> Optional[Tuple[float, float]]:
        """Calculate trend line slope and intercept"""
        try:
            if len(values) < 2:
                return None

            n = len(values)
            x = list(range(n))

            # Calculate slope using least squares
            sum_x = sum(x)
            sum_y = sum(values)
            sum_xy = sum(x[i] * values[i] for i in range(n))
            sum_x2 = sum(xi * xi for xi in x)

            denominator = n * sum_x2 - sum_x * sum_x
            if abs(denominator) < 1e-10:
                return None

            slope = (n * sum_xy - sum_x * sum_y) / denominator
            intercept = (sum_y - slope * sum_x) / n

            return slope, intercept

        except Exception as e:
            logger.error(f"Trend line calculation failed: {str(e)}")
            return None

    def _detect_continuation_pattern(self, prices: List[float], timestamps: List[float],
                                   pattern_name: str, config: Dict) -> Optional[Dict[str, Any]]:
        """Detect continuation patterns (flags, pennants)"""
        try:
            if len(prices) < 15:
                return None

            # Look for strong move followed by consolidation
            # Find the strongest move in recent data
            max_move = 0
            move_start = 0
            move_end = 0

            for i in range(len(prices) - 10):
                for j in range(i + 5, min(i + 15, len(prices))):
                    move_size = abs(prices[j] - prices[i]) / prices[i]
                    if move_size > max_move:
                        max_move = move_size
                        move_start = i
                        move_end = j

            if max_move < 0.03:  # Minimum 3% move required
                return None

            # Check for consolidation after the move
            consolidation_start = move_end
            consolidation_end = min(move_end + 10, len(prices) - 1)

            if consolidation_end - consolidation_start < 5:
                return None

            consolidation_prices = prices[consolidation_start:consolidation_end]
            consolidation_range = max(consolidation_prices) - min(consolidation_prices)
            consolidation_avg = sum(consolidation_prices) / len(consolidation_prices)

            # Pattern should have tight consolidation (< 2% range)
            if consolidation_range / consolidation_avg > 0.02:
                return None

            # Determine signal direction based on initial move
            signal_type = 'BUY' if prices[move_end] > prices[move_start] else 'SELL'

            return {
                'pattern': pattern_name,
                'confidence': min(max_move * 10, 1.0),
                'strength': config['strength'],
                'reliability': config['reliability'],
                'reversal': config['reversal'],
                'key_levels': {
                    'flagpole_start': prices[move_start],
                    'flagpole_end': prices[move_end],
                    'consolidation_high': max(consolidation_prices),
                    'consolidation_low': min(consolidation_prices)
                },
                'signal_type': signal_type,
                'description': f"{pattern_name.replace('_', ' ').title()} pattern after {max_move:.1%} move",
                'pattern_points': {
                    'flagpole': {
                        'start': {'timestamp': timestamps[move_start], 'price': prices[move_start]},
                        'end': {'timestamp': timestamps[move_end], 'price': prices[move_end]}
                    },
                    'consolidation': {
                        'high': max(consolidation_prices),
                        'low': min(consolidation_prices),
                        'avg': consolidation_avg
                    }
                }
            }

        except Exception as e:
            logger.error(f"Continuation pattern detection failed: {str(e)}")
            return None

    def _detect_wedge_pattern(self, prices: List[float], timestamps: List[float],
                             pattern_name: str, config: Dict) -> Optional[Dict[str, Any]]:
        """Detect wedge patterns (rising/falling wedges)"""
        try:
            if len(prices) < 20:
                return None

            # Find highs and lows for trend line analysis
            highs = []
            lows = []

            for i in range(2, len(prices) - 2):
                if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                    highs.append((i, prices[i]))
                elif prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                    lows.append((i, prices[i]))

            if len(highs) < 3 or len(lows) < 3:
                return None

            # Calculate trend lines
            high_trend = self._calculate_trend_line([h[1] for h in highs[-4:]])
            low_trend = self._calculate_trend_line([l[1] for l in lows[-4:]])

            if high_trend is None or low_trend is None:
                return None

            high_slope, _ = high_trend
            low_slope, _ = low_trend

            # Determine wedge type
            wedge_type = None
            signal_type = 'NEUTRAL'

            if high_slope > 0.001 and low_slope > 0.001 and high_slope < low_slope:
                # Both lines rising, but lower line rising faster (converging upward)
                wedge_type = 'wedge_rising'
                signal_type = 'SELL'  # Rising wedge is bearish
            elif high_slope < -0.001 and low_slope < -0.001 and high_slope > low_slope:
                # Both lines falling, but upper line falling faster (converging downward)
                wedge_type = 'wedge_falling'
                signal_type = 'BUY'  # Falling wedge is bullish

            if wedge_type == pattern_name:
                # Calculate pattern strength based on convergence
                latest_high = highs[-1][1]
                latest_low = lows[-1][1]
                pattern_height = latest_high - latest_low

                # Check if lines are actually converging
                initial_height = highs[0][1] - lows[0][1] if len(highs) > 0 and len(lows) > 0 else pattern_height
                convergence_ratio = pattern_height / initial_height if initial_height > 0 else 1.0

                if convergence_ratio < 0.8:  # Lines should be converging
                    strength = min((1 - convergence_ratio) * 2, 1.0)

                    return {
                        'pattern': wedge_type,
                        'confidence': min(strength * 1.5, 1.0),
                        'strength': config['strength'],
                        'reliability': config['reliability'],
                        'reversal': config['reversal'],
                        'key_levels': {
                            'upper_trendline': latest_high,
                            'lower_trendline': latest_low,
                            'convergence_area': (latest_high + latest_low) / 2
                        },
                        'signal_type': signal_type,
                        'description': f"{wedge_type.replace('_', ' ').title()} pattern detected",
                        'pattern_points': {
                            'highs': [{'timestamp': timestamps[h[0]], 'price': h[1]} for h in highs[-4:]],
                            'lows': [{'timestamp': timestamps[l[0]], 'price': l[1]} for l in lows[-4:]]
                        }
                    }

            return None

        except Exception as e:
            logger.error(f"Wedge pattern detection failed: {str(e)}")
            return None

    def _simulate_multi_timeframe_analysis(self, ocr_data: Dict, price_action: Dict,
                                         technical: Dict) -> Dict[str, Any]:
        """
        Simulate multi-timeframe analysis based on current timeframe data
        """
        try:
            current_tf = ocr_data.get('timeframe', 'H1')
            current_trend = price_action.get('trend', 'NEUTRAL')
            current_momentum = technical.get('overall_momentum', 'NEUTRAL')

            # Simulate higher timeframe analysis
            htf_analysis = self._simulate_higher_timeframe(current_tf, current_trend, current_momentum)

            # Simulate lower timeframe analysis
            ltf_analysis = self._simulate_lower_timeframe(current_tf, current_trend, current_momentum)

            # Calculate timeframe alignment
            alignment_score = self._calculate_timeframe_alignment(htf_analysis, current_trend, ltf_analysis)

            return {
                'current_timeframe': current_tf,
                'higher_timeframe': htf_analysis,
                'lower_timeframe': ltf_analysis,
                'alignment_score': alignment_score,
                'alignment_description': self._describe_timeframe_alignment(alignment_score),
                'trading_bias': self._determine_mtf_bias(htf_analysis, current_trend, ltf_analysis),
                'confidence_multiplier': self._calculate_mtf_confidence_multiplier(alignment_score)
            }

        except Exception as e:
            logger.error(f"Multi-timeframe analysis simulation failed: {str(e)}")
            return {
                'current_timeframe': 'UNKNOWN',
                'alignment_score': 0.5,
                'trading_bias': 'NEUTRAL',
                'confidence_multiplier': 1.0
            }

    def _simulate_higher_timeframe(self, current_tf: str, current_trend: str, current_momentum: str) -> Dict[str, Any]:
        """Simulate higher timeframe analysis"""
        # Map to higher timeframe
        tf_hierarchy = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1']

        try:
            current_idx = tf_hierarchy.index(current_tf)
            higher_tf = tf_hierarchy[min(current_idx + 2, len(tf_hierarchy) - 1)]
        except ValueError:
            higher_tf = 'H4'  # Default

        # Simulate higher timeframe trend (usually more stable)
        htf_trend = current_trend
        if current_momentum in ['STRONG_BULLISH', 'STRONG_BEARISH']:
            # Strong momentum usually aligns with higher timeframe
            htf_trend = 'BULLISH' if 'BULLISH' in current_momentum else 'BEARISH'
        elif current_trend == 'NEUTRAL':
            # If current is neutral, HTF might still have direction
            htf_trend = 'RANGING'

        return {
            'timeframe': higher_tf,
            'trend': htf_trend,
            'strength': 0.7,  # HTF trends are generally stronger
            'support_resistance': 'STRONG',
            'momentum': 'STABLE'
        }

    def _simulate_lower_timeframe(self, current_tf: str, current_trend: str, current_momentum: str) -> Dict[str, Any]:
        """Simulate lower timeframe analysis"""
        # Map to lower timeframe
        tf_hierarchy = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1']

        try:
            current_idx = tf_hierarchy.index(current_tf)
            lower_tf = tf_hierarchy[max(current_idx - 2, 0)]
        except ValueError:
            lower_tf = 'M15'  # Default

        # Lower timeframe is usually more volatile and noisy
        ltf_trend = current_trend
        if current_momentum == 'NEUTRAL':
            ltf_trend = 'CHOPPY'
        elif 'WEAK' in current_momentum:
            ltf_trend = 'CONSOLIDATING'

        return {
            'timeframe': lower_tf,
            'trend': ltf_trend,
            'strength': 0.4,  # LTF trends are generally weaker
            'noise_level': 'HIGH',
            'volatility': 'ELEVATED'
        }

    def _calculate_timeframe_alignment(self, htf: Dict, current_trend: str, ltf: Dict) -> float:
        """Calculate alignment score between timeframes"""
        alignment_score = 0.5  # Base neutral score

        htf_trend = htf.get('trend', 'NEUTRAL')
        ltf_trend = ltf.get('trend', 'NEUTRAL')

        # Perfect alignment
        if htf_trend == current_trend == ltf_trend and htf_trend != 'NEUTRAL':
            alignment_score = 0.9
        # HTF and current align
        elif htf_trend == current_trend and htf_trend != 'NEUTRAL':
            alignment_score = 0.8
        # Current and LTF align
        elif current_trend == ltf_trend and current_trend != 'NEUTRAL':
            alignment_score = 0.6
        # All different or neutral
        elif htf_trend != current_trend != ltf_trend:
            alignment_score = 0.3

        return alignment_score

    def _describe_timeframe_alignment(self, alignment_score: float) -> str:
        """Describe timeframe alignment"""
        if alignment_score >= 0.8:
            return "Excellent alignment across timeframes - high probability setup"
        elif alignment_score >= 0.6:
            return "Good alignment - favorable trading conditions"
        elif alignment_score >= 0.4:
            return "Moderate alignment - proceed with caution"
        else:
            return "Poor alignment - conflicting timeframes, avoid trading"

    def _determine_mtf_bias(self, htf: Dict, current_trend: str, ltf: Dict) -> str:
        """Determine multi-timeframe trading bias"""
        htf_trend = htf.get('trend', 'NEUTRAL')
        ltf_trend = ltf.get('trend', 'NEUTRAL')

        # HTF has highest priority
        if htf_trend in ['BULLISH', 'STRONG_BULLISH']:
            return 'BULLISH'
        elif htf_trend in ['BEARISH', 'STRONG_BEARISH']:
            return 'BEARISH'
        elif current_trend in ['BULLISH', 'BEARISH']:
            return current_trend
        else:
            return 'NEUTRAL'

    def _calculate_mtf_confidence_multiplier(self, alignment_score: float) -> float:
        """Calculate confidence multiplier based on timeframe alignment"""
        if alignment_score >= 0.8:
            return 1.3  # Boost confidence for aligned timeframes
        elif alignment_score >= 0.6:
            return 1.1
        elif alignment_score >= 0.4:
            return 1.0
        else:
            return 0.7  # Reduce confidence for conflicting timeframes

    def _analyze_signal_confluence_advanced(self, price_action: Dict, smc: Dict,
                                          technical: Dict, advanced_patterns: List[Dict]) -> List[Dict[str, Any]]:
        """Advanced signal confluence analysis"""
        try:
            confluence_signals = []

            # Collect all signals
            all_signals = []

            # Price action signals
            pa_signals = price_action.get('signals', [])
            for signal in pa_signals:
                signal['source'] = 'price_action'
                all_signals.append(signal)

            # SMC signals
            smc_signals = smc.get('smc_signals', [])
            for signal in smc_signals:
                signal['source'] = 'smc'
                all_signals.append(signal)

            # Technical signals
            tech_signals = technical.get('technical_signals', [])
            for signal in tech_signals:
                signal['source'] = 'technical'
                all_signals.append(signal)

            # Pattern signals
            for pattern in advanced_patterns:
                if pattern.get('signal_type') != 'NEUTRAL':
                    pattern_signal = {
                        'type': pattern.get('signal_type'),
                        'strength': pattern.get('confidence', 0.5),
                        'confidence': pattern.get('reliability', 0.5),
                        'source': 'pattern',
                        'pattern_name': pattern.get('pattern'),
                        'description': pattern.get('description', '')
                    }
                    all_signals.append(pattern_signal)

            # Group signals by price level/area
            signal_groups = self._group_signals_by_area(all_signals)

            # Find confluent areas
            for group in signal_groups:
                if len(group['signals']) >= self.filtering_criteria['confluence_requirement']:
                    confluence_score = self._calculate_confluence_score(group['signals'])

                    if confluence_score >= 0.6:  # Minimum confluence threshold
                        confluence_signals.append({
                            'area': group['area'],
                            'signals': group['signals'],
                            'confluence_score': confluence_score,
                            'signal_count': len(group['signals']),
                            'dominant_type': self._get_dominant_signal_type(group['signals']),
                            'strength': self._calculate_group_strength(group['signals']),
                            'sources': list(set(s.get('source', 'unknown') for s in group['signals']))
                        })

            # Sort by confluence score
            confluence_signals.sort(key=lambda x: x['confluence_score'], reverse=True)

            return confluence_signals[:3]  # Return top 3 confluence areas

        except Exception as e:
            logger.error(f"Advanced signal confluence analysis failed: {str(e)}")
            return []

    def _group_signals_by_area(self, signals: List[Dict]) -> List[Dict[str, Any]]:
        """Group signals by price area/level"""
        try:
            # For now, we'll use a simple grouping approach
            # In a real implementation, this would use actual price levels

            groups = []
            used_signals = set()

            for i, signal in enumerate(signals):
                if i in used_signals:
                    continue

                group = {
                    'area': f"area_{len(groups)}",
                    'signals': [signal]
                }
                used_signals.add(i)

                # Find nearby signals (simplified approach)
                for j, other_signal in enumerate(signals[i+1:], i+1):
                    if j in used_signals:
                        continue

                    # Simple grouping logic - in real implementation would use price proximity
                    if (signal.get('source') != other_signal.get('source') and
                        signal.get('type') == other_signal.get('type')):
                        group['signals'].append(other_signal)
                        used_signals.add(j)

                groups.append(group)

            return groups

        except Exception as e:
            logger.error(f"Signal grouping failed: {str(e)}")
            return []

    def _calculate_confluence_score(self, signals: List[Dict]) -> float:
        """Calculate confluence score for a group of signals"""
        try:
            if not signals:
                return 0.0

            # Base score from signal count
            count_score = min(len(signals) / 5.0, 1.0)  # Max at 5 signals

            # Diversity score (different sources)
            sources = set(s.get('source', 'unknown') for s in signals)
            diversity_score = min(len(sources) / 4.0, 1.0)  # Max at 4 different sources

            # Strength score (average signal strength)
            strengths = [s.get('strength', 0.5) for s in signals]
            strength_score = sum(strengths) / len(strengths)

            # Consistency score (same signal type)
            signal_types = [s.get('type') for s in signals]
            most_common_type = max(set(signal_types), key=signal_types.count)
            consistency_score = signal_types.count(most_common_type) / len(signal_types)

            # Weighted confluence score
            confluence_score = (
                count_score * 0.3 +
                diversity_score * 0.3 +
                strength_score * 0.2 +
                consistency_score * 0.2
            )

            return min(confluence_score, 1.0)

        except Exception as e:
            logger.error(f"Confluence score calculation failed: {str(e)}")
            return 0.0

    def _get_dominant_signal_type(self, signals: List[Dict]) -> str:
        """Get the dominant signal type in a group"""
        try:
            signal_types = [s.get('type') for s in signals if s.get('type')]
            if not signal_types:
                return 'NEUTRAL'

            return max(set(signal_types), key=signal_types.count)

        except Exception as e:
            logger.error(f"Dominant signal type calculation failed: {str(e)}")
            return 'NEUTRAL'

    def _calculate_group_strength(self, signals: List[Dict]) -> float:
        """Calculate overall strength of a signal group"""
        try:
            if not signals:
                return 0.0

            strengths = [s.get('strength', 0.5) for s in signals]
            confidences = [s.get('confidence', 0.5) for s in signals]

            # Weighted average with confidence as weight
            total_weight = sum(confidences)
            if total_weight == 0:
                return sum(strengths) / len(strengths)

            weighted_strength = sum(s * c for s, c in zip(strengths, confidences)) / total_weight
            return min(weighted_strength, 1.0)

        except Exception as e:
            logger.error(f"Group strength calculation failed: {str(e)}")
            return 0.0

    def _apply_advanced_filtering(self, confluence_signals: List[Dict], image: np.ndarray) -> List[Dict[str, Any]]:
        """Apply advanced filtering to remove noise and weak signals"""
        try:
            filtered_signals = []

            for signal_group in confluence_signals:
                # Apply filtering criteria
                if self._passes_advanced_filters(signal_group, image):
                    filtered_signals.append(signal_group)

            return filtered_signals

        except Exception as e:
            logger.error(f"Advanced filtering failed: {str(e)}")
            return confluence_signals  # Return unfiltered if error

    def _passes_advanced_filters(self, signal_group: Dict, image: np.ndarray) -> bool:
        """Check if signal group passes advanced filtering criteria"""
        try:
            # Minimum confluence score
            if signal_group.get('confluence_score', 0) < 0.4:
                return False

            # Minimum signal count
            if signal_group.get('signal_count', 0) < 2:
                return False

            # Minimum strength
            if signal_group.get('strength', 0) < self.filtering_criteria['min_pattern_strength']:
                return False

            # Check for noise level in image area (simplified)
            noise_level = self._estimate_noise_level(image)
            if noise_level > self.filtering_criteria['max_noise_ratio']:
                # Reduce confidence for noisy conditions
                signal_group['confidence_adjustment'] = 0.8

            return True

        except Exception as e:
            logger.error(f"Filter check failed: {str(e)}")
            return True  # Default to pass if error

    def _estimate_noise_level(self, image: np.ndarray) -> float:
        """Estimate noise level in the image"""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Calculate image variance as noise indicator
            variance = np.var(gray)

            # Normalize to 0-1 range (simplified approach)
            normalized_variance = min(variance / 10000.0, 1.0)

            return normalized_variance

        except Exception as e:
            logger.error(f"Noise level estimation failed: {str(e)}")
            return 0.5  # Default moderate noise

    def _enhance_signal_strength(self, filtered_signals: List[Dict], mtf_analysis: Dict) -> List[Dict[str, Any]]:
        """Enhance signal strength based on multi-timeframe analysis"""
        try:
            enhanced_signals = []

            mtf_multiplier = mtf_analysis.get('confidence_multiplier', 1.0)
            trading_bias = mtf_analysis.get('trading_bias', 'NEUTRAL')

            for signal in filtered_signals:
                enhanced_signal = signal.copy()

                # Apply MTF multiplier
                original_strength = signal.get('strength', 0.5)
                enhanced_strength = min(original_strength * mtf_multiplier, 1.0)

                # Boost signals that align with MTF bias
                dominant_type = signal.get('dominant_type', 'NEUTRAL')
                if ((trading_bias == 'BULLISH' and dominant_type == 'BUY') or
                    (trading_bias == 'BEARISH' and dominant_type == 'SELL')):
                    enhanced_strength = min(enhanced_strength * 1.2, 1.0)

                enhanced_signal['enhanced_strength'] = enhanced_strength
                enhanced_signal['mtf_alignment'] = trading_bias == dominant_type.replace('BUY', 'BULLISH').replace('SELL', 'BEARISH')
                enhanced_signal['original_strength'] = original_strength

                enhanced_signals.append(enhanced_signal)

            # Sort by enhanced strength
            enhanced_signals.sort(key=lambda x: x.get('enhanced_strength', 0), reverse=True)

            return enhanced_signals

        except Exception as e:
            logger.error(f"Signal strength enhancement failed: {str(e)}")
            return filtered_signals

    def _generate_advanced_explanations(self, enhanced_signals: List[Dict],
                                       advanced_patterns: List[Dict]) -> List[Dict[str, Any]]:
        """Generate advanced explanations for signals"""
        try:
            explanations = []

            for signal in enhanced_signals:
                explanation = {
                    'signal_area': signal.get('area', 'unknown'),
                    'confluence_score': signal.get('confluence_score', 0),
                    'signal_count': signal.get('signal_count', 0),
                    'sources': signal.get('sources', []),
                    'dominant_type': signal.get('dominant_type', 'NEUTRAL'),
                    'strength_analysis': {
                        'original': signal.get('original_strength', 0),
                        'enhanced': signal.get('enhanced_strength', 0),
                        'mtf_aligned': signal.get('mtf_alignment', False)
                    },
                    'explanation': self._generate_signal_explanation(signal),
                    'confidence_factors': self._identify_confidence_factors(signal),
                    'risk_factors': self._identify_risk_factors(signal)
                }
                explanations.append(explanation)

            return explanations

        except Exception as e:
            logger.error(f"Advanced explanation generation failed: {str(e)}")
            return []

    def _generate_signal_explanation(self, signal: Dict) -> str:
        """Generate human-readable explanation for a signal"""
        try:
            signal_type = signal.get('dominant_type', 'NEUTRAL')
            confluence_score = signal.get('confluence_score', 0)
            signal_count = signal.get('signal_count', 0)
            sources = signal.get('sources', [])

            explanation = f"Sinyal {signal_type} dengan confluence score {confluence_score:.2f} "
            explanation += f"dari {signal_count} indikator berbeda: {', '.join(sources)}. "

            if confluence_score >= 0.8:
                explanation += "Confluence sangat kuat menunjukkan probabilitas tinggi untuk pergerakan harga."
            elif confluence_score >= 0.6:
                explanation += "Confluence yang baik memberikan setup trading yang solid."
            else:
                explanation += "Confluence moderat, perlu konfirmasi tambahan."

            return explanation

        except Exception as e:
            logger.error(f"Signal explanation generation failed: {str(e)}")
            return "Error dalam generate penjelasan sinyal"

    def _identify_confidence_factors(self, signal: Dict) -> List[str]:
        """Identify factors that increase confidence in the signal"""
        factors = []

        if signal.get('confluence_score', 0) >= 0.8:
            factors.append("Confluence score sangat tinggi")

        if signal.get('signal_count', 0) >= 4:
            factors.append("Banyak indikator mendukung")

        if signal.get('mtf_alignment', False):
            factors.append("Selaras dengan bias multi-timeframe")

        sources = signal.get('sources', [])
        if len(sources) >= 3:
            factors.append("Diversitas sumber analisis yang baik")

        if 'pattern' in sources:
            factors.append("Didukung oleh pattern recognition")

        return factors

    def _identify_risk_factors(self, signal: Dict) -> List[str]:
        """Identify factors that increase risk in the signal"""
        factors = []

        if signal.get('confluence_score', 0) < 0.6:
            factors.append("Confluence score relatif rendah")

        if signal.get('signal_count', 0) < 3:
            factors.append("Jumlah indikator pendukung terbatas")

        if not signal.get('mtf_alignment', True):
            factors.append("Tidak selaras dengan bias multi-timeframe")

        if signal.get('confidence_adjustment', 1.0) < 1.0:
            factors.append("Kondisi market noise tinggi")

        return factors

    def _calculate_advanced_analysis_score(self, enhanced_signals: List[Dict],
                                         advanced_patterns: List[Dict], mtf_analysis: Dict) -> Dict[str, Any]:
        """Calculate overall advanced analysis score"""
        try:
            if not enhanced_signals:
                return {'overall_score': 0.0, 'grade': 'F', 'description': 'Tidak ada sinyal valid'}

            # Calculate component scores
            signal_quality_score = sum(s.get('enhanced_strength', 0) for s in enhanced_signals) / len(enhanced_signals)
            pattern_score = sum(p.get('confidence', 0) for p in advanced_patterns) / max(len(advanced_patterns), 1)
            mtf_score = mtf_analysis.get('alignment_score', 0.5)

            # Weighted overall score
            overall_score = (
                signal_quality_score * 0.5 +
                pattern_score * 0.3 +
                mtf_score * 0.2
            )

            # Determine grade
            if overall_score >= 0.8:
                grade = 'A'
                description = 'Excellent - Setup trading berkualitas tinggi'
            elif overall_score >= 0.7:
                grade = 'B'
                description = 'Good - Setup trading yang solid'
            elif overall_score >= 0.6:
                grade = 'C'
                description = 'Fair - Setup trading acceptable'
            elif overall_score >= 0.4:
                grade = 'D'
                description = 'Poor - Setup trading lemah'
            else:
                grade = 'F'
                description = 'Fail - Hindari trading'

            return {
                'overall_score': overall_score,
                'grade': grade,
                'description': description,
                'component_scores': {
                    'signal_quality': signal_quality_score,
                    'pattern_quality': pattern_score,
                    'mtf_alignment': mtf_score
                }
            }

        except Exception as e:
            logger.error(f"Advanced analysis score calculation failed: {str(e)}")
            return {'overall_score': 0.0, 'grade': 'F', 'description': 'Error dalam perhitungan score'}

    def _calculate_signal_quality_metrics(self, enhanced_signals: List[Dict]) -> Dict[str, Any]:
        """Calculate signal quality metrics"""
        try:
            if not enhanced_signals:
                return {'average_quality': 0.0, 'quality_distribution': {}, 'total_signals': 0}

            qualities = [s.get('enhanced_strength', 0) for s in enhanced_signals]
            average_quality = sum(qualities) / len(qualities)

            # Quality distribution
            distribution = {'high': 0, 'medium': 0, 'low': 0}
            for quality in qualities:
                if quality >= 0.7:
                    distribution['high'] += 1
                elif quality >= 0.5:
                    distribution['medium'] += 1
                else:
                    distribution['low'] += 1

            return {
                'average_quality': average_quality,
                'quality_distribution': distribution,
                'total_signals': len(enhanced_signals),
                'highest_quality': max(qualities),
                'lowest_quality': min(qualities)
            }

        except Exception as e:
            logger.error(f"Signal quality metrics calculation failed: {str(e)}")
            return {'average_quality': 0.0, 'quality_distribution': {}, 'total_signals': 0}

    def _calculate_recommendation_confidence(self, enhanced_signals: List[Dict], mtf_analysis: Dict) -> Dict[str, Any]:
        """Calculate recommendation confidence"""
        try:
            if not enhanced_signals:
                return {'confidence': 0.0, 'level': 'very_low', 'factors': []}

            # Base confidence from signal strength
            signal_strengths = [s.get('enhanced_strength', 0) for s in enhanced_signals]
            base_confidence = sum(signal_strengths) / len(signal_strengths)

            # MTF alignment boost
            mtf_multiplier = mtf_analysis.get('confidence_multiplier', 1.0)
            adjusted_confidence = min(base_confidence * mtf_multiplier, 1.0)

            # Determine confidence level
            if adjusted_confidence >= 0.8:
                level = 'very_high'
            elif adjusted_confidence >= 0.7:
                level = 'high'
            elif adjusted_confidence >= 0.5:
                level = 'medium'
            elif adjusted_confidence >= 0.3:
                level = 'low'
            else:
                level = 'very_low'

            # Confidence factors
            factors = []
            if len(enhanced_signals) >= 3:
                factors.append("Multiple confluent signals")
            if mtf_analysis.get('alignment_score', 0) >= 0.7:
                factors.append("Strong timeframe alignment")
            if base_confidence >= 0.7:
                factors.append("High signal quality")

            return {
                'confidence': adjusted_confidence,
                'level': level,
                'factors': factors,
                'base_confidence': base_confidence,
                'mtf_adjustment': mtf_multiplier
            }

        except Exception as e:
            logger.error(f"Recommendation confidence calculation failed: {str(e)}")
            return {'confidence': 0.0, 'level': 'very_low', 'factors': []}

    def _assess_advanced_risks(self, enhanced_signals: List[Dict], advanced_patterns: List[Dict]) -> Dict[str, Any]:
        """Assess advanced risk factors"""
        try:
            risk_factors = []
            risk_level = 'medium'  # Default

            # Signal-based risks
            if not enhanced_signals:
                risk_factors.append("No valid signals detected")
                risk_level = 'very_high'
            elif len(enhanced_signals) == 1:
                risk_factors.append("Single signal - lack of confirmation")
                risk_level = 'high'

            # Pattern-based risks
            reversal_patterns = [p for p in advanced_patterns if p.get('reversal', False)]
            if reversal_patterns:
                risk_factors.append("Reversal patterns detected - trend change possible")

            # Quality-based risks
            avg_strength = sum(s.get('enhanced_strength', 0) for s in enhanced_signals) / max(len(enhanced_signals), 1)
            if avg_strength < 0.5:
                risk_factors.append("Below average signal strength")
                risk_level = 'high'

            # Determine overall risk level
            if len(risk_factors) >= 3:
                risk_level = 'very_high'
            elif len(risk_factors) >= 2:
                risk_level = 'high'
            elif len(risk_factors) == 1:
                risk_level = 'medium'
            else:
                risk_level = 'low'

            return {
                'risk_level': risk_level,
                'risk_factors': risk_factors,
                'risk_score': len(risk_factors) / 5.0,  # Normalize to 0-1
                'mitigation_suggestions': self._suggest_risk_mitigation(risk_factors)
            }

        except Exception as e:
            logger.error(f"Advanced risk assessment failed: {str(e)}")
            return {'risk_level': 'high', 'risk_factors': ['Error in risk assessment'], 'risk_score': 0.8}

    def _suggest_risk_mitigation(self, risk_factors: List[str]) -> List[str]:
        """Suggest risk mitigation strategies"""
        suggestions = []

        for factor in risk_factors:
            if 'single signal' in factor.lower():
                suggestions.append("Wait for additional confirmation signals")
            elif 'reversal' in factor.lower():
                suggestions.append("Use tighter stop losses and smaller position sizes")
            elif 'strength' in factor.lower():
                suggestions.append("Consider waiting for stronger setup")
            elif 'no valid signals' in factor.lower():
                suggestions.append("Avoid trading until clear signals emerge")

        if not suggestions:
            suggestions.append("Use standard risk management practices")

        return suggestions

    def _determine_optimal_timing(self, enhanced_signals: List[Dict], mtf_analysis: Dict) -> Dict[str, Any]:
        """Determine optimal timing for trade execution"""
        try:
            if not enhanced_signals:
                return {'timing': 'wait', 'reason': 'No valid signals', 'urgency': 'none'}

            # Analyze signal urgency
            avg_strength = sum(s.get('enhanced_strength', 0) for s in enhanced_signals) / len(enhanced_signals)
            mtf_alignment = mtf_analysis.get('alignment_score', 0.5)

            if avg_strength >= 0.8 and mtf_alignment >= 0.7:
                timing = 'immediate'
                urgency = 'high'
                reason = 'Strong signals with excellent timeframe alignment'
            elif avg_strength >= 0.6 and mtf_alignment >= 0.5:
                timing = 'soon'
                urgency = 'medium'
                reason = 'Good signals with acceptable alignment'
            elif avg_strength >= 0.4:
                timing = 'wait_for_confirmation'
                urgency = 'low'
                reason = 'Moderate signals - wait for additional confirmation'
            else:
                timing = 'wait'
                urgency = 'none'
                reason = 'Weak signals - wait for better setup'

            return {
                'timing': timing,
                'reason': reason,
                'urgency': urgency,
                'signal_strength': avg_strength,
                'mtf_alignment': mtf_alignment
            }

        except Exception as e:
            logger.error(f"Optimal timing determination failed: {str(e)}")
            return {'timing': 'wait', 'reason': 'Error in timing analysis', 'urgency': 'none'}

    def _generate_default_advanced_analysis(self) -> Dict[str, Any]:
        """Generate default analysis result for error cases"""
        return {
            'advanced_patterns': [],
            'multi_timeframe_analysis': {
                'current_timeframe': 'UNKNOWN',
                'alignment_score': 0.0,
                'trading_bias': 'NEUTRAL',
                'confidence_multiplier': 1.0
            },
            'confluence_signals': [],
            'filtered_signals': [],
            'enhanced_signals': [],
            'signal_explanations': [],
            'analysis_score': {'overall_score': 0.0, 'grade': 'F', 'description': 'Error in analysis'},
            'signal_quality_metrics': {'average_quality': 0.0, 'total_signals': 0},
            'recommendation_confidence': {'confidence': 0.0, 'level': 'very_low'},
            'risk_assessment': {'risk_level': 'very_high', 'risk_factors': ['System error']},
            'execution_timing': {'timing': 'wait', 'reason': 'System error', 'urgency': 'none'},
            'analysis_metadata': {
                'timestamp': datetime.now().isoformat(),
                'version': 'advanced_v1.0_error',
                'error': True
            }
        }
