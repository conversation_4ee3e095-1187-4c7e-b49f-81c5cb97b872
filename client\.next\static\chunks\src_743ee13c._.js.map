{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/auto-analysis/client/src/components/ImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, X, Image as ImageIcon, Loader2, CheckCircle, AlertCircle } from 'lucide-react';\nimport axios from 'axios';\n\ninterface ImageUploadProps {\n  onAnalysisComplete: (data: any) => void;\n  onAnalysisStart: () => void;\n  onReset: () => void;\n  isAnalyzing: boolean;\n}\n\nexport default function ImageUpload({ \n  onAnalysisComplete, \n  onAnalysisStart, \n  onReset, \n  isAnalyzing \n}: ImageUploadProps) {\n  const [uploadedFile, setUploadedFile] = useState<File | null>(null);\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\n  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');\n  const [errorMessage, setErrorMessage] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const file = acceptedFiles[0];\n    if (file) {\n      setUploadedFile(file);\n      setPreviewUrl(URL.createObjectURL(file));\n      setUploadStatus('idle');\n      setErrorMessage('');\n    }\n  }, []);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']\n    },\n    maxFiles: 1,\n    maxSize: 10 * 1024 * 1024, // 10MB\n  });\n\n  const handleUploadAndAnalyze = async () => {\n    if (!uploadedFile) return;\n\n    setUploadStatus('uploading');\n    onAnalysisStart();\n\n    try {\n      // Upload file\n      const formData = new FormData();\n      formData.append('image', uploadedFile);\n\n      const uploadResponse = await axios.post('http://localhost:5000/api/upload', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      if (uploadResponse.data.success) {\n        setUploadStatus('success');\n        const uploadId = uploadResponse.data.data.id;\n\n        // Start analysis\n        const analysisResponse = await axios.post(`http://localhost:5000/api/analysis/start/${uploadId}`);\n\n        if (analysisResponse.data.success) {\n          // Poll for results\n          pollForResults(uploadId);\n        } else {\n          throw new Error('Failed to start analysis');\n        }\n      } else {\n        throw new Error('Upload failed');\n      }\n    } catch (error: any) {\n      console.error('Upload/Analysis error:', error);\n      setUploadStatus('error');\n      setErrorMessage(error.response?.data?.message || error.message || 'Upload failed');\n      onAnalysisComplete(null);\n    }\n  };\n\n  const pollForResults = async (uploadId: string) => {\n    const maxAttempts = 60; // 5 minutes with 5-second intervals\n    let attempts = 0;\n\n    const poll = async () => {\n      try {\n        attempts++;\n        const response = await axios.get(`http://localhost:5000/api/analysis/upload/${uploadId}`);\n\n        if (response.data.success && response.data.data) {\n          // Analysis completed\n          onAnalysisComplete(response.data.data);\n        } else if (response.data.status === 'processing') {\n          // Still processing, continue polling\n          if (attempts < maxAttempts) {\n            setTimeout(poll, 5000); // Poll every 5 seconds\n          } else {\n            throw new Error('Analysis timeout');\n          }\n        } else {\n          throw new Error('Analysis failed');\n        }\n      } catch (error: any) {\n        console.error('Polling error:', error);\n        setUploadStatus('error');\n        setErrorMessage('Analysis failed or timed out');\n        onAnalysisComplete(null);\n      }\n    };\n\n    poll();\n  };\n\n  const handleReset = () => {\n    setUploadedFile(null);\n    setPreviewUrl(null);\n    setUploadStatus('idle');\n    setErrorMessage('');\n    onReset();\n  };\n\n  const getStatusIcon = () => {\n    switch (uploadStatus) {\n      case 'uploading':\n        return <Loader2 className=\"w-5 h-5 text-blue-500 animate-spin\" />;\n      case 'success':\n        return <CheckCircle className=\"w-5 h-5 text-green-500\" />;\n      case 'error':\n        return <AlertCircle className=\"w-5 h-5 text-red-500\" />;\n      default:\n        return null;\n    }\n  };\n\n  const getStatusText = () => {\n    if (isAnalyzing) return 'Menganalisis...';\n    switch (uploadStatus) {\n      case 'uploading':\n        return 'Mengupload...';\n      case 'success':\n        return 'Upload berhasil';\n      case 'error':\n        return errorMessage || 'Upload gagal';\n      default:\n        return '';\n    }\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Upload Area */}\n      {!uploadedFile ? (\n        <div\n          {...getRootProps()}\n          className={`\n            border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200\n            ${isDragActive \n              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' \n              : 'border-slate-300 dark:border-slate-600 hover:border-blue-400 dark:hover:border-blue-500'\n            }\n          `}\n        >\n          <input {...getInputProps()} />\n          <div className=\"flex flex-col items-center space-y-4\">\n            <div className=\"w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center\">\n              <Upload className=\"w-8 h-8 text-slate-500 dark:text-slate-400\" />\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-slate-900 dark:text-white mb-2\">\n                {isDragActive ? 'Drop file di sini...' : 'Upload Chart Trading'}\n              </p>\n              <p className=\"text-sm text-slate-500 dark:text-slate-400\">\n                Drag & drop atau klik untuk memilih file\n              </p>\n              <p className=\"text-xs text-slate-400 dark:text-slate-500 mt-1\">\n                PNG, JPG, JPEG hingga 10MB\n              </p>\n            </div>\n          </div>\n        </div>\n      ) : (\n        /* Preview Area */\n        <div className=\"space-y-4\">\n          <div className=\"relative bg-slate-50 dark:bg-slate-700 rounded-xl p-4\">\n            <button\n              onClick={handleReset}\n              className=\"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors z-10\"\n            >\n              <X className=\"w-4 h-4\" />\n            </button>\n            \n            {previewUrl && (\n              <div className=\"relative\">\n                <img\n                  src={previewUrl}\n                  alt=\"Preview\"\n                  className=\"w-full h-64 object-contain rounded-lg\"\n                />\n                <div className=\"absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs\">\n                  {uploadedFile.name}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Status */}\n          {(uploadStatus !== 'idle' || isAnalyzing) && (\n            <div className=\"flex items-center space-x-2 p-3 bg-slate-50 dark:bg-slate-700 rounded-lg\">\n              {getStatusIcon()}\n              <span className=\"text-sm font-medium text-slate-700 dark:text-slate-300\">\n                {getStatusText()}\n              </span>\n            </div>\n          )}\n\n          {/* Action Button */}\n          <button\n            onClick={handleUploadAndAnalyze}\n            disabled={!uploadedFile || uploadStatus === 'uploading' || isAnalyzing}\n            className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2\"\n          >\n            {(uploadStatus === 'uploading' || isAnalyzing) ? (\n              <>\n                <Loader2 className=\"w-5 h-5 animate-spin\" />\n                <span>{isAnalyzing ? 'Menganalisis...' : 'Mengupload...'}</span>\n              </>\n            ) : (\n              <>\n                <ImageIcon className=\"w-5 h-5\" />\n                <span>Analisis Chart</span>\n              </>\n            )}\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAce,SAAS,YAAY,KAKjB;QALiB,EAClC,kBAAkB,EAClB,eAAe,EACf,OAAO,EACP,WAAW,EACM,GALiB;;IAMlC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8C;IAC7F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,CAAC;YAC1B,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,IAAI,MAAM;gBACR,gBAAgB;gBAChB,cAAc,IAAI,eAAe,CAAC;gBAClC,gBAAgB;gBAChB,gBAAgB;YAClB;QACF;0CAAG,EAAE;IAEL,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAQ;gBAAQ;aAAQ;QAC/D;QACA,UAAU;QACV,SAAS,KAAK,OAAO;IACvB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,cAAc;QAEnB,gBAAgB;QAChB;QAEA,IAAI;YACF,cAAc;YACd,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YAEzB,MAAM,iBAAiB,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,oCAAoC,UAAU;gBACpF,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,eAAe,IAAI,CAAC,OAAO,EAAE;gBAC/B,gBAAgB;gBAChB,MAAM,WAAW,eAAe,IAAI,CAAC,IAAI,CAAC,EAAE;gBAE5C,iBAAiB;gBACjB,MAAM,mBAAmB,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,AAAC,4CAAoD,OAAT;gBAEtF,IAAI,iBAAiB,IAAI,CAAC,OAAO,EAAE;oBACjC,mBAAmB;oBACnB,eAAe;gBACjB,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAY;gBAGH,sBAAA;YAFhB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,gBAAgB;YAChB,gBAAgB,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI,MAAM,OAAO,IAAI;YAClE,mBAAmB;QACrB;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,MAAM,cAAc,IAAI,oCAAoC;QAC5D,IAAI,WAAW;QAEf,MAAM,OAAO;YACX,IAAI;gBACF;gBACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,AAAC,6CAAqD,OAAT;gBAE9E,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;oBAC/C,qBAAqB;oBACrB,mBAAmB,SAAS,IAAI,CAAC,IAAI;gBACvC,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,cAAc;oBAChD,qCAAqC;oBACrC,IAAI,WAAW,aAAa;wBAC1B,WAAW,MAAM,OAAO,uBAAuB;oBACjD,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,gBAAgB;gBAChB,gBAAgB;gBAChB,mBAAmB;YACrB;QACF;QAEA;IACF;IAEA,MAAM,cAAc;QAClB,gBAAgB;QAChB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,aAAa,OAAO;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,gBAAgB;YACzB;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBAEZ,CAAC,6BACA,6LAAC;YACE,GAAG,cAAc;YAClB,WAAW,AAAC,2HAKT,OAHC,eACE,mDACA,2FACH;;8BAGH,6LAAC;oBAAO,GAAG,eAAe;;;;;;8BAC1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CACV,eAAe,yBAAyB;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;;;;;;;;;;;;mBAOrE,gBAAgB,iBAChB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;wBAGd,4BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,KAAK;oCACL,KAAI;oCACJ,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;8CACZ,aAAa,IAAI;;;;;;;;;;;;;;;;;;gBAOzB,CAAC,iBAAiB,UAAU,WAAW,mBACtC,6LAAC;oBAAI,WAAU;;wBACZ;sCACD,6LAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;8BAMP,6LAAC;oBACC,SAAS;oBACT,UAAU,CAAC,gBAAgB,iBAAiB,eAAe;oBAC3D,WAAU;8BAET,AAAC,iBAAiB,eAAe,4BAChC;;0CACE,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;0CAAM,cAAc,oBAAoB;;;;;;;qDAG3C;;0CACE,6LAAC,uMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GApOwB;;QAqBgC,2KAAA,CAAA,cAAW;;;KArB3C", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/auto-analysis/client/src/components/AnalysisResults.tsx"], "sourcesContent": ["'use client';\n\nimport { TrendingUp, TrendingDown, Minus, Target, Shield, DollarSign, BarChart3, Loader2 } from 'lucide-react';\n\ninterface AnalysisResultsProps {\n  data: any;\n  isAnalyzing: boolean;\n}\n\nexport default function AnalysisResults({ data, isAnalyzing }: AnalysisResultsProps) {\n  if (isAnalyzing) {\n    return (\n      <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n        <div className=\"flex items-center justify-center py-12\">\n          <div className=\"text-center\">\n            <Loader2 className=\"w-12 h-12 text-blue-500 animate-spin mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n              Menganalisis Chart...\n            </h3>\n            <p className=\"text-slate-600 dark:text-slate-300\">\n              Sedang memproses Price Action, SMC, dan Technical Indicators\n            </p>\n            <div className=\"mt-4 flex justify-center space-x-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"></div>\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!data) {\n    return (\n      <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n        <div className=\"text-center py-12\">\n          <BarChart3 className=\"w-16 h-16 text-slate-300 dark:text-slate-600 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n            Hasil Analisis\n          </h3>\n          <p className=\"text-slate-600 dark:text-slate-300\">\n            Upload chart trading untuk melihat analisis mendalam\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  // Parse results if it's a string\n  let results;\n  try {\n    results = typeof data.results === 'string' ? JSON.parse(data.results) : data.results;\n  } catch (error) {\n    results = data.results || {};\n  }\n\n  const recommendation = data.recommendation || results?.decision?.recommendation || 'HOLD';\n  const confidence = data.confidence || results?.decision?.confidence || 0;\n  const entryPrice = data.entry_price || results?.decision?.entry_price || 0;\n  const stopLoss = data.stop_loss || results?.decision?.stop_loss || 0;\n  const takeProfit = data.take_profit || results?.decision?.take_profit || 0;\n  const riskReward = data.risk_reward_ratio || results?.decision?.risk_reward_ratio || 0;\n\n  const getRecommendationIcon = () => {\n    switch (recommendation) {\n      case 'BUY':\n        return <TrendingUp className=\"w-6 h-6 text-green-500\" />;\n      case 'SELL':\n        return <TrendingDown className=\"w-6 h-6 text-red-500\" />;\n      default:\n        return <Minus className=\"w-6 h-6 text-yellow-500\" />;\n    }\n  };\n\n  const getRecommendationColor = () => {\n    switch (recommendation) {\n      case 'BUY':\n        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';\n      case 'SELL':\n        return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';\n      default:\n        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20';\n    }\n  };\n\n  const getConfidenceColor = () => {\n    if (confidence >= 0.8) return 'text-green-600 dark:text-green-400';\n    if (confidence >= 0.6) return 'text-yellow-600 dark:text-yellow-400';\n    return 'text-red-600 dark:text-red-400';\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Main Recommendation */}\n      <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n        <h2 className=\"text-2xl font-semibold text-slate-900 dark:text-white mb-6\">\n          Rekomendasi Trading\n        </h2>\n        \n        <div className=\"flex items-center justify-between mb-6\">\n          <div className={`flex items-center space-x-3 px-4 py-3 rounded-xl ${getRecommendationColor()}`}>\n            {getRecommendationIcon()}\n            <span className=\"text-2xl font-bold\">{recommendation}</span>\n          </div>\n          \n          <div className=\"text-right\">\n            <div className=\"text-sm text-slate-500 dark:text-slate-400\">Confidence</div>\n            <div className={`text-2xl font-bold ${getConfidenceColor()}`}>\n              {(confidence * 100).toFixed(1)}%\n            </div>\n          </div>\n        </div>\n\n        {/* Price Levels */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n          <div className=\"bg-slate-50 dark:bg-slate-700 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <DollarSign className=\"w-4 h-4 text-blue-500\" />\n              <span className=\"text-sm font-medium text-slate-600 dark:text-slate-300\">Entry Price</span>\n            </div>\n            <div className=\"text-xl font-bold text-slate-900 dark:text-white\">\n              {entryPrice > 0 ? entryPrice.toFixed(4) : 'N/A'}\n            </div>\n          </div>\n\n          <div className=\"bg-slate-50 dark:bg-slate-700 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Shield className=\"w-4 h-4 text-red-500\" />\n              <span className=\"text-sm font-medium text-slate-600 dark:text-slate-300\">Stop Loss</span>\n            </div>\n            <div className=\"text-xl font-bold text-red-600 dark:text-red-400\">\n              {stopLoss > 0 ? stopLoss.toFixed(4) : 'N/A'}\n            </div>\n          </div>\n\n          <div className=\"bg-slate-50 dark:bg-slate-700 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Target className=\"w-4 h-4 text-green-500\" />\n              <span className=\"text-sm font-medium text-slate-600 dark:text-slate-300\">Take Profit</span>\n            </div>\n            <div className=\"text-xl font-bold text-green-600 dark:text-green-400\">\n              {takeProfit > 0 ? takeProfit.toFixed(4) : 'N/A'}\n            </div>\n          </div>\n        </div>\n\n        {/* Risk Reward */}\n        {riskReward > 0 && (\n          <div className=\"mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm font-medium text-slate-600 dark:text-slate-300\">Risk-Reward Ratio</span>\n              <span className=\"text-lg font-bold text-blue-600 dark:text-blue-400\">\n                1:{riskReward.toFixed(2)}\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Analysis Details */}\n      {results && (\n        <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n          <h3 className=\"text-xl font-semibold text-slate-900 dark:text-white mb-4\">\n            Detail Analisis\n          </h3>\n          \n          <div className=\"space-y-4\">\n            {/* Price Action */}\n            {results.price_action && (\n              <div className=\"p-4 bg-slate-50 dark:bg-slate-700 rounded-xl\">\n                <h4 className=\"font-semibold text-slate-900 dark:text-white mb-2\">Price Action</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Trend: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.price_action.trend || 'N/A'}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Signal: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.price_action.signal || 'N/A'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* SMC */}\n            {results.smc && (\n              <div className=\"p-4 bg-slate-50 dark:bg-slate-700 rounded-xl\">\n                <h4 className=\"font-semibold text-slate-900 dark:text-white mb-2\">Smart Money Concept</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Structure: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.smc.market_structure || 'N/A'}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Signal: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.smc.signal || 'N/A'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Technical Indicators */}\n            {results.technical && (\n              <div className=\"p-4 bg-slate-50 dark:bg-slate-700 rounded-xl\">\n                <h4 className=\"font-semibold text-slate-900 dark:text-white mb-2\">Technical Indicators</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">RSI: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.technical.rsi?.value ? results.technical.rsi.value.toFixed(2) : 'N/A'}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">MACD: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.technical.macd?.signal || 'N/A'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AASe,SAAS,gBAAgB,KAA2C;QAA3C,EAAE,IAAI,EAAE,WAAW,EAAwB,GAA3C;QAgDQ,mBACR,oBACC,oBACJ,oBACI,oBACM,oBA4JxB,wBAMA;IAtNrB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,6LAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAGlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CACjG,6LAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM7G;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAM1D;IAEA,iCAAiC;IACjC,IAAI;IACJ,IAAI;QACF,UAAU,OAAO,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,CAAC,KAAK,OAAO,IAAI,KAAK,OAAO;IACtF,EAAE,OAAO,OAAO;QACd,UAAU,KAAK,OAAO,IAAI,CAAC;IAC7B;IAEA,MAAM,iBAAiB,KAAK,cAAc,KAAI,oBAAA,+BAAA,oBAAA,QAAS,QAAQ,cAAjB,wCAAA,kBAAmB,cAAc,KAAI;IACnF,MAAM,aAAa,KAAK,UAAU,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,UAAU,KAAI;IACvE,MAAM,aAAa,KAAK,WAAW,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,WAAW,KAAI;IACzE,MAAM,WAAW,KAAK,SAAS,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,SAAS,KAAI;IACnE,MAAM,aAAa,KAAK,WAAW,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,WAAW,KAAI;IACzE,MAAM,aAAa,KAAK,iBAAiB,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,iBAAiB,KAAI;IAErF,MAAM,wBAAwB;QAC5B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,cAAc,KAAK,OAAO;QAC9B,IAAI,cAAc,KAAK,OAAO;QAC9B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAI3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,oDAA4E,OAAzB;;oCACjE;kDACD,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6C;;;;;;kDAC5D,6LAAC;wCAAI,WAAW,AAAC,sBAA0C,OAArB;;4CACnC,CAAC,aAAa,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;kCAMrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;kDACZ,aAAa,IAAI,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;0CAI9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;kDACZ,WAAW,IAAI,SAAS,OAAO,CAAC,KAAK;;;;;;;;;;;;0CAI1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;kDACZ,aAAa,IAAI,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;oBAM/C,aAAa,mBACZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAyD;;;;;;8CACzE,6LAAC;oCAAK,WAAU;;wCAAqD;wCAChE,WAAW,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;YAQ/B,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAI1E,6LAAC;wBAAI,WAAU;;4BAEZ,QAAQ,YAAY,kBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,YAAY,CAAC,KAAK,IAAI;;;;;;;;;;;;0DAGnC,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,YAAY,CAAC,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;4BAQzC,QAAQ,GAAG,kBACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,GAAG,CAAC,gBAAgB,IAAI;;;;;;;;;;;;0DAGrC,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,GAAG,CAAC,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;4BAQhC,QAAQ,SAAS,kBAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,EAAA,yBAAA,QAAQ,SAAS,CAAC,GAAG,cAArB,6CAAA,uBAAuB,KAAK,IAAG,QAAQ,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAG7E,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,EAAA,0BAAA,QAAQ,SAAS,CAAC,IAAI,cAAtB,8CAAA,wBAAwB,MAAM,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD;KAlOwB", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/auto-analysis/client/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>u, X, TrendingUp, BarChart3 } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white/80 dark:bg-slate-900/80 backdrop-blur-md border-b border-slate-200 dark:border-slate-700 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold text-slate-900 dark:text-white\">\n                Market Analysis\n              </h1>\n              <p className=\"text-xs text-slate-500 dark:text-slate-400\">\n                AI-Powered Trading Insights\n              </p>\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a \n              href=\"#\" \n              className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n            >\n              Dashboard\n            </a>\n            <a \n              href=\"#\" \n              className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n            >\n              History\n            </a>\n            <a \n              href=\"#\" \n              className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n            >\n              Settings\n            </a>\n          </nav>\n\n          {/* Stats */}\n          <div className=\"hidden lg:flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-2\">\n              <BarChart3 className=\"w-4 h-4 text-green-500\" />\n              <div className=\"text-sm\">\n                <div className=\"text-slate-900 dark:text-white font-semibold\">98.5%</div>\n                <div className=\"text-slate-500 dark:text-slate-400 text-xs\">Accuracy</div>\n              </div>\n            </div>\n            <div className=\"w-px h-8 bg-slate-200 dark:bg-slate-700\"></div>\n            <div className=\"text-sm\">\n              <div className=\"text-slate-900 dark:text-white font-semibold\">1,247</div>\n              <div className=\"text-slate-500 dark:text-slate-400 text-xs\">Analyses</div>\n            </div>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors\"\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-slate-600 dark:text-slate-300\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-slate-600 dark:text-slate-300\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-slate-200 dark:border-slate-700\">\n            <nav className=\"flex flex-col space-y-4\">\n              <a \n                href=\"#\" \n                className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n              >\n                Dashboard\n              </a>\n              <a \n                href=\"#\" \n                className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n              >\n                History\n              </a>\n              <a \n                href=\"#\" \n                className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n              >\n                Settings\n              </a>\n              \n              {/* Mobile Stats */}\n              <div className=\"flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700\">\n                <div className=\"flex items-center space-x-2\">\n                  <BarChart3 className=\"w-4 h-4 text-green-500\" />\n                  <div className=\"text-sm\">\n                    <span className=\"text-slate-900 dark:text-white font-semibold\">98.5% </span>\n                    <span className=\"text-slate-500 dark:text-slate-400\">Accuracy</span>\n                  </div>\n                </div>\n                <div className=\"text-sm\">\n                  <span className=\"text-slate-900 dark:text-white font-semibold\">1,247 </span>\n                  <span className=\"text-slate-500 dark:text-slate-400\">Analyses</span>\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmD;;;;;;sDAGjE,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;sCAO9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA+C;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA+C;;;;;;sDAC9D,6LAAC;4CAAI,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;sCAKhE,6LAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAKD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA+C;;;;;;kEAC/D,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;kDAGzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA+C;;;;;;0DAC/D,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvE;GApHwB;KAAA", "debugId": null}}, {"offset": {"line": 1460, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/auto-analysis/client/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport ImageUpload from '@/components/ImageUpload';\nimport AnalysisResults from '@/components/AnalysisResults';\nimport Header from '@/components/Header';\n\nexport default function Home() {\n  const [analysisData, setAnalysisData] = useState(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n\n  const handleAnalysisComplete = (data: any) => {\n    setAnalysisData(data);\n    setIsAnalyzing(false);\n  };\n\n  const handleAnalysisStart = () => {\n    setIsAnalyzing(true);\n    setAnalysisData(null);\n  };\n\n  const handleReset = () => {\n    setAnalysisData(null);\n    setIsAnalyzing(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800\">\n      <Header />\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Hero Section */}\n          <div className=\"text-center mb-12\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-slate-900 dark:text-white mb-6\">\n              Market Trend\n              <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                {' '}Analysis\n              </span>\n            </h1>\n            <p className=\"text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed\">\n              Upload screenshot chart trading Anda dan dapatkan analisis mendalam menggunakan\n              Price Action, Smart Money Concept, dan Technical Indicators untuk rekomendasi trading yang akurat.\n            </p>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"grid lg:grid-cols-2 gap-8\">\n            {/* Upload Section */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n                <h2 className=\"text-2xl font-semibold text-slate-900 dark:text-white mb-4\">\n                  Upload Chart Trading\n                </h2>\n                <ImageUpload\n                  onAnalysisComplete={handleAnalysisComplete}\n                  onAnalysisStart={handleAnalysisStart}\n                  onReset={handleReset}\n                  isAnalyzing={isAnalyzing}\n                />\n              </div>\n\n              {/* Features */}\n              <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n                <h3 className=\"text-xl font-semibold text-slate-900 dark:text-white mb-4\">\n                  Fitur Analisis\n                </h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Price Action Analysis</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Smart Money Concept</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Technical Indicators</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">OCR Text Recognition</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Results Section */}\n            <div className=\"space-y-6\">\n              <AnalysisResults\n                data={analysisData}\n                isAnalyzing={isAnalyzing}\n              />\n            </div>\n          </div>\n\n          {/* Info Cards */}\n          <div className=\"grid md:grid-cols-3 gap-6 mt-12\">\n            <div className=\"bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-slate-700\">\n              <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n                Analisis Komprehensif\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-300 text-sm\">\n                Kombinasi Price Action, SMC, dan Technical Indicators untuk analisis yang mendalam dan akurat.\n              </p>\n            </div>\n\n            <div className=\"bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-slate-700\">\n              <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n                Rekomendasi Cepat\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-300 text-sm\">\n                Dapatkan rekomendasi BUY/SELL/HOLD dengan confidence level dan risk management yang jelas.\n              </p>\n            </div>\n\n            <div className=\"bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-slate-700\">\n              <div className=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n                Risk Management\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-300 text-sm\">\n                Stop Loss, Take Profit, dan Risk-Reward Ratio yang dihitung otomatis untuk trading yang aman.\n              </p>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,yBAAyB,CAAC;QAC9B,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,sBAAsB;QAC1B,eAAe;QACf,gBAAgB;IAClB;IAEA,MAAM,cAAc;QAClB,gBAAgB;QAChB,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAqE;sDAEjF,6LAAC;4CAAK,WAAU;;gDACb;gDAAI;;;;;;;;;;;;;8CAGT,6LAAC;oCAAE,WAAU;8CAA+E;;;;;;;;;;;;sCAO9F,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6D;;;;;;8DAG3E,6LAAC,oIAAA,CAAA,UAAW;oDACV,oBAAoB;oDACpB,iBAAiB;oDACjB,SAAS;oDACT,aAAa;;;;;;;;;;;;sDAKjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;;;;;;;sEAEvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;;;;;;;sEAEvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;;;;;;;sEAEvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,wIAAA,CAAA,UAAe;wCACd,MAAM;wCACN,aAAa;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA2C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAClG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAK5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA6C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACpG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAK5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA+C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;GA1IwB;KAAA", "debugId": null}}]}