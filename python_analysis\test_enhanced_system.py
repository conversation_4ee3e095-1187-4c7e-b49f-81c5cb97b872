#!/usr/bin/env python3
"""
Test script untuk memverifikasi sistem analisis yang telah ditingkatkan
"""

import sys
import os
import numpy as np
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test semua import yang diperlukan"""
    print("🔍 Testing imports...")
    
    try:
        from modules.decision_engine import EnhancedDecisionEngine
        print("✅ EnhancedDecisionEngine imported successfully")
        
        from modules.advanced_signal_analyzer import AdvancedSignalAnalyzer
        print("✅ AdvancedSignalAnalyzer imported successfully")
        
        from main import MarketAnalysisEngine
        print("✅ MarketAnalysisEngine imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {str(e)}")
        return False

def test_enhanced_decision_engine():
    """Test Enhanced Decision Engine"""
    print("\n🧠 Testing Enhanced Decision Engine...")
    
    try:
        from modules.decision_engine import EnhancedDecisionEngine
        
        engine = EnhancedDecisionEngine()
        print("✅ EnhancedDecisionEngine initialized successfully")
        
        # Test dengan data dummy
        dummy_price_action = {
            'signals': [{'type': 'BUY', 'strength': 0.7, 'confidence': 0.8}],
            'trend': 'BULLISH',
            'support_resistance': {'support': 1.2000, 'resistance': 1.2100}
        }
        
        dummy_smc = {
            'smc_signals': [{'type': 'BUY', 'strength': 0.6, 'confidence': 0.7}],
            'market_structure': 'BULLISH',
            'order_blocks': []
        }
        
        dummy_technical = {
            'technical_signals': [{'type': 'BUY', 'strength': 0.8, 'confidence': 0.9}],
            'overall_momentum': 'BULLISH',
            'rsi': {'value': 45, 'signal': 'NEUTRAL'}
        }
        
        dummy_ocr = {
            'symbol': 'EURUSD',
            'timeframe': 'H1',
            'current_price': 1.2050
        }
        
        decision = engine.make_decision(dummy_price_action, dummy_smc, dummy_technical, dummy_ocr)
        
        print(f"✅ Decision made: {decision['recommendation']}")
        print(f"✅ Confidence: {decision['confidence']:.2f}")
        print(f"✅ Market regime: {decision.get('market_regime', 'unknown')}")
        
        return True
    except Exception as e:
        print(f"❌ Enhanced Decision Engine error: {str(e)}")
        return False

def test_advanced_signal_analyzer():
    """Test Advanced Signal Analyzer"""
    print("\n📊 Testing Advanced Signal Analyzer...")
    
    try:
        from modules.advanced_signal_analyzer import AdvancedSignalAnalyzer
        
        analyzer = AdvancedSignalAnalyzer()
        print("✅ AdvancedSignalAnalyzer initialized successfully")
        
        # Create dummy image
        dummy_image = np.random.randint(0, 255, (400, 600, 3), dtype=np.uint8)
        
        # Test dengan data dummy
        dummy_price_action = {
            'signals': [{'type': 'BUY', 'strength': 0.7, 'confidence': 0.8}],
            'trend': 'BULLISH'
        }
        
        dummy_smc = {
            'smc_signals': [{'type': 'BUY', 'strength': 0.6, 'confidence': 0.7}]
        }
        
        dummy_technical = {
            'technical_signals': [{'type': 'BUY', 'strength': 0.8, 'confidence': 0.9}],
            'overall_momentum': 'BULLISH'
        }
        
        dummy_ocr = {
            'symbol': 'EURUSD',
            'timeframe': 'H1',
            'current_price': 1.2050
        }
        
        analysis = analyzer.analyze_advanced_signals(
            dummy_image, dummy_price_action, dummy_smc, dummy_technical, dummy_ocr
        )
        
        print(f"✅ Advanced analysis completed")
        print(f"✅ Analysis score: {analysis.get('analysis_score', {}).get('overall_score', 0):.2f}")
        print(f"✅ Patterns detected: {len(analysis.get('advanced_patterns', []))}")
        print(f"✅ MTF alignment: {analysis.get('multi_timeframe_analysis', {}).get('alignment_score', 0):.2f}")
        
        return True
    except Exception as e:
        print(f"❌ Advanced Signal Analyzer error: {str(e)}")
        return False

def test_integration():
    """Test integrasi sistem lengkap"""
    print("\n🔗 Testing System Integration...")
    
    try:
        from main import MarketAnalysisEngine
        
        engine = MarketAnalysisEngine()
        print("✅ MarketAnalysisEngine initialized with all components")
        
        # Verify all components are initialized
        assert hasattr(engine, 'decision_engine'), "Missing decision_engine"
        assert hasattr(engine, 'advanced_signal_analyzer'), "Missing advanced_signal_analyzer"
        
        print("✅ All components properly integrated")
        
        return True
    except Exception as e:
        print(f"❌ Integration error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Enhanced Market Analysis System Tests")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_enhanced_decision_engine,
        test_advanced_signal_analyzer,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Sistem analisis telah berhasil ditingkatkan!")
        print("\n✨ Fitur-fitur baru yang tersedia:")
        print("   • Enhanced Decision Engine dengan market regime detection")
        print("   • Advanced pattern recognition")
        print("   • Multi-timeframe analysis simulation")
        print("   • Signal confluence analysis")
        print("   • Detailed signal reasoning dan explanations")
        print("   • Enhanced risk management")
        print("   • Comprehensive confidence scoring")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
