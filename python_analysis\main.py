#!/usr/bin/env python3
"""
Market Trend Analysis Engine
Main entry point for analyzing trading chart images
"""

import sys
import json
import os
import traceback
from datetime import datetime
import logging

# Import analysis modules
from modules.image_processor import ImageProcessor
from modules.ocr_extractor import OCRExtractor
from modules.price_action_analyzer import PriceActionAnalyzer
from modules.smc_analyzer import SMCAnalyzer
from modules.technical_indicators import TechnicalIndicators
from modules.decision_engine import DecisionEngine

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MarketAnalysisEngine:
    """Main analysis engine that coordinates all analysis modules"""
    
    def __init__(self):
        self.image_processor = ImageProcessor()
        self.ocr_extractor = OCRExtractor()
        self.price_action_analyzer = PriceActionAnalyzer()
        self.smc_analyzer = SMCAnalyzer()
        self.technical_indicators = TechnicalIndicators()
        self.decision_engine = DecisionEngine()
        
    def analyze_chart(self, image_path, upload_id):
        """
        Main analysis function that processes a chart image
        
        Args:
            image_path (str): Path to the chart image
            upload_id (str): Upload ID from database
            
        Returns:
            dict: Complete analysis results
        """
        try:
            logger.info(f"Starting analysis for image: {image_path}")
            
            # Step 1: Process and prepare image
            logger.info("Step 1: Processing image...")
            processed_image = self.image_processor.process_image(image_path)
            
            if processed_image is None:
                raise Exception("Failed to process image")
            
            # Step 2: Extract text and price data using OCR
            logger.info("Step 2: Extracting OCR data...")
            ocr_data = self.ocr_extractor.extract_data(processed_image)
            
            # Step 3: Analyze price action patterns
            logger.info("Step 3: Analyzing price action...")
            price_action_signals = self.price_action_analyzer.analyze(processed_image, ocr_data)
            
            # Step 4: Apply Smart Money Concept analysis
            logger.info("Step 4: Analyzing Smart Money Concepts...")
            smc_signals = self.smc_analyzer.analyze(processed_image, ocr_data)
            
            # Step 5: Calculate technical indicators
            logger.info("Step 5: Calculating technical indicators...")
            technical_data = self.technical_indicators.calculate(processed_image, ocr_data)
            
            # Step 6: Make final decision
            logger.info("Step 6: Making trading decision...")
            decision = self.decision_engine.make_decision(
                price_action_signals,
                smc_signals,
                technical_data,
                ocr_data
            )
            
            # Compile results
            results = {
                "success": True,
                "upload_id": upload_id,
                "timestamp": datetime.now().isoformat(),
                "ocr_data": {
                    "extracted_text": ocr_data.get("text", ""),
                    "symbol": ocr_data.get("symbol", "UNKNOWN"),
                    "timeframe": ocr_data.get("timeframe", "UNKNOWN"),
                    "current_price": ocr_data.get("current_price", 0.0),
                    "price_data": ocr_data.get("price_data", {}),
                    "chart_metadata": ocr_data.get("metadata", {})
                },
                "analysis": {
                    "recommendation": decision["recommendation"],
                    "confidence_score": decision["confidence"],
                    "entry_price": decision["entry_price"],
                    "stop_loss": decision["stop_loss"],
                    "take_profit": decision["take_profit"],
                    "risk_reward_ratio": decision["risk_reward_ratio"],
                    "price_action_signals": price_action_signals,
                    "smc_signals": smc_signals,
                    "rsi_analysis": technical_data.get("rsi", {}),
                    "macd_analysis": technical_data.get("macd", {}),
                    "moving_averages": technical_data.get("moving_averages", {}),
                    "support_resistance": technical_data.get("support_resistance", {}),
                    "analysis_notes": decision.get("notes", ""),
                    "market_structure": smc_signals.get("market_structure", ""),
                    "trend_direction": price_action_signals.get("trend", "NEUTRAL")
                }
            }
            
            logger.info("Analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Analysis failed: {str(e)}")
            logger.error(traceback.format_exc())
            
            return {
                "success": False,
                "upload_id": upload_id,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "ocr_data": None,
                "analysis": None
            }

def main():
    """Main entry point"""
    try:
        # Check command line arguments
        if len(sys.argv) != 3:
            print(json.dumps({
                "success": False,
                "error": "Usage: python main.py <image_path> <upload_id>",
                "timestamp": datetime.now().isoformat()
            }))
            sys.exit(1)
        
        image_path = sys.argv[1]
        upload_id = sys.argv[2]
        
        # Validate image path
        if not os.path.exists(image_path):
            print(json.dumps({
                "success": False,
                "error": f"Image file not found: {image_path}",
                "timestamp": datetime.now().isoformat()
            }))
            sys.exit(1)
        
        # Initialize and run analysis
        engine = MarketAnalysisEngine()
        results = engine.analyze_chart(image_path, upload_id)
        
        # Output results as JSON
        print(json.dumps(results, indent=2))
        
        # Exit with appropriate code
        sys.exit(0 if results["success"] else 1)
        
    except Exception as e:
        # Handle any unexpected errors
        error_result = {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
            "timestamp": datetime.now().isoformat(),
            "traceback": traceback.format_exc()
        }
        
        print(json.dumps(error_result, indent=2))
        sys.exit(1)

if __name__ == "__main__":
    main()
