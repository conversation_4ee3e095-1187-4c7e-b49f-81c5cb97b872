{"timestamp":"2025-07-25T17:40:53.942Z","level":"INFO","message":"File uploaded successfully: chart_6f8acaef-2b4e-4ee6-8156-2f6557e66781.png","originalName":"sample_chart.png","size":307858,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:40:54.365Z","level":"ERROR","message":"Failed to save upload record","error":"Access denied for user 'root'@'localhost' (using password: NO)"}
{"timestamp":"2025-07-25T17:42:40.879Z","level":"INFO","message":"File uploaded successfully: chart_f0365c8e-4080-4508-b5fe-fba7363e6f86.png","originalName":"sample_chart.png","size":307858,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:42:40.932Z","level":"INFO","message":"Upload record created with ID: 1"}
{"timestamp":"2025-07-25T17:42:46.431Z","level":"INFO","message":"Starting analysis for upload 1"}
{"timestamp":"2025-07-25T17:42:46.433Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_f0365c8e-4080-4508-b5fe-fba7363e6f86.png 1"}
{"timestamp":"2025-07-25T17:42:47.060Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:47,060 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_f0365c8e-4080-4508-b5fe-fba7363e6f86.png\r\n2025-07-26 00:42:47,060 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:42:47.071Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:47,071 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:42:48.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:48,992 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:42:48.993Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:48,992 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:42:49.144Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,144 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:42:49.144Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,144 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:42:49,144 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:42:49.145Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,145 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:42:49.146Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,146 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-25T17:42:49.147Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,146 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:42:49.156Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,157 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:42:49.251Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,249 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-25T17:42:49.252Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,250 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 00:42:49,250 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:42:49.254Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,255 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:42:49.260Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,260 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:42:49.287Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,288 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:42:49.288Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,288 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:42:49,288 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:42:49,288 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:42:49.370Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,370 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:42:49.371Z","level":"WARN","message":"Python stderr: 2025-07-26 00:42:49,370 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:42:49,370 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:42:49,371 - modules.decision_engine - INFO - Decision completed: HOLD with 0.46 confidence\r\n2025-07-26 00:42:49,371 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:42:49.373Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"1\",\r\n  \"timestamp\": \"2025-07-26T00:42:49.371159\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.46464960000000005,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"NEUTRAL\",\r\n      \"trend_strength\": 0.0,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        },\r\n        {\r\n          \"type\": \"triangle\",\r\n          \"area\": 18973.0,\r\n          \"vertices\": 3,\r\n          \"strength\": 1.0\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1577.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.3154\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.1,\r\n      \"price_action_score\": 0.0\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3123,\r\n      \"signal_line\": 0.3123,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"NEUTRAL\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:42:49.475Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:42:49.476Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:42:49.485Z","level":"INFO","message":"Analysis completed for upload 1"}
{"timestamp":"2025-07-25T17:43:36.914Z","level":"INFO","message":"File uploaded successfully: chart_840fa8ca-e45c-4dad-900e-7afe9b9a540a.png","originalName":"sample_chart.png","size":307858,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:43:36.920Z","level":"INFO","message":"Upload record created with ID: 2"}
{"timestamp":"2025-07-25T17:43:36.940Z","level":"INFO","message":"Starting analysis for upload 2"}
{"timestamp":"2025-07-25T17:43:36.941Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_840fa8ca-e45c-4dad-900e-7afe9b9a540a.png 2"}
{"timestamp":"2025-07-25T17:43:37.616Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:37,616 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_840fa8ca-e45c-4dad-900e-7afe9b9a540a.png\r\n2025-07-26 00:43:37,616 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:43:37.629Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:37,629 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:43:39.885Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,884 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:43:39.886Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,884 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:43:39.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,992 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:43:39.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,992 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:43:39,992 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:43:39.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,993 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:43:39.995Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,995 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-25T17:43:39.996Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:39,995 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:43:40.003Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,003 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:43:40.101Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,100 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-25T17:43:40.103Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,101 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 00:43:40,101 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:43:40.108Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,107 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:43:40.114Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,114 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:43:40.139Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,140 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:43:40.141Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,140 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:43:40,140 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:43:40,140 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:43:40.225Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,225 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:43:40.226Z","level":"WARN","message":"Python stderr: 2025-07-26 00:43:40,225 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:43:40,226 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:43:40,226 - modules.decision_engine - INFO - Decision completed: HOLD with 0.46 confidence\r\n2025-07-26 00:43:40,226 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:43:40.227Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"2\",\r\n  \"timestamp\": \"2025-07-26T00:43:40.226161\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.46464960000000005,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"NEUTRAL\",\r\n      \"trend_strength\": 0.0,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        },\r\n        {\r\n          \"type\": \"triangle\",\r\n          \"area\": 18973.0,\r\n          \"vertices\": 3,\r\n          \"strength\": 1.0\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1577.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.3154\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.1,\r\n      \"price_action_score\": 0.0\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3123,\r\n      \"signal_line\": 0.3123,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"NEUTRAL\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:43:40.325Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:43:40.326Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:43:40.335Z","level":"INFO","message":"Analysis completed for upload 2"}
{"timestamp":"2025-07-25T17:44:37.078Z","level":"INFO","message":"File uploaded successfully: chart_0408232a-a747-472f-a180-c6639423440d.png","originalName":"sample_chart.png","size":307858,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:44:37.082Z","level":"INFO","message":"Upload record created with ID: 3"}
{"timestamp":"2025-07-25T17:44:37.307Z","level":"INFO","message":"Starting analysis for upload 3"}
{"timestamp":"2025-07-25T17:44:37.307Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_0408232a-a747-472f-a180-c6639423440d.png 3"}
{"timestamp":"2025-07-25T17:44:37.977Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:37,977 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_0408232a-a747-472f-a180-c6639423440d.png"}
{"timestamp":"2025-07-25T17:44:37.978Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:37,977 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:44:37.992Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:37,992 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:44:40.319Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,318 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:44:40.321Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,319 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:44:40.440Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,440 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:44:40.441Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,440 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:44:40,441 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:44:40.441Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,441 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:44:40.443Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,442 - __main__ - INFO - Step 3: Analyzing price action...\r\n2025-07-26 00:44:40,442 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:44:40.452Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,451 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:44:40.560Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,559 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL"}
{"timestamp":"2025-07-25T17:44:40.561Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,559 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts...\r\n2025-07-26 00:44:40,559 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:44:40.565Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,565 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:44:40.573Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,573 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:44:40.602Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,602 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:44:40.604Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,603 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:44:40,603 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:44:40,603 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:44:40.691Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,690 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:44:40.693Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"3\",\r\n  \"timestamp\": \"2025-07-26T00:44:40.691532\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.46464960000000005,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"NEUTRAL\",\r\n      \"trend_strength\": 0.0,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        },\r\n        {\r\n          \"type\": \"triangle\",\r\n          \"area\": 18973.0,\r\n          \"vertices\": 3,\r\n          \"strength\": 1.0\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1577.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.3154\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.1,\r\n      \"price_action_score\": 0.0\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3123,\r\n      \"signal_line\": 0.3123,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"NEUTRAL\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:44:40.694Z","level":"WARN","message":"Python stderr: 2025-07-26 00:44:40,691 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:44:40,691 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:44:40,691 - modules.decision_engine - INFO - Decision completed: HOLD with 0.46 confidence\r\n2025-07-26 00:44:40,691 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:44:40.803Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:44:40.804Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:44:40.812Z","level":"INFO","message":"Analysis completed for upload 3"}
{"timestamp":"2025-07-25T17:46:46.728Z","level":"INFO","message":"File uploaded successfully: chart_fc21fe10-acc0-44e0-833f-fc2fa18e8798.png","originalName":"image.png","size":316272,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:46:46.733Z","level":"INFO","message":"Upload record created with ID: 4"}
{"timestamp":"2025-07-25T17:46:46.751Z","level":"INFO","message":"Starting analysis for upload 4"}
{"timestamp":"2025-07-25T17:46:46.752Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_fc21fe10-acc0-44e0-833f-fc2fa18e8798.png 4"}
{"timestamp":"2025-07-25T17:46:47.369Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:47,369 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_fc21fe10-acc0-44e0-833f-fc2fa18e8798.png"}
{"timestamp":"2025-07-25T17:46:47.370Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:47,369 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:46:47.383Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:47,382 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:46:49.521Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,520 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:46:49.523Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,520 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:46:49.645Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,645 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:46:49.646Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,645 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:46:49,645 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:46:49.646Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,646 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:46:49.647Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,647 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-25T17:46:49.648Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,647 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:46:49.654Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,653 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:46:49.747Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,746 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: BULLISH\r\n2025-07-26 00:46:49,746 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts..."}
{"timestamp":"2025-07-25T17:46:49.747Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,746 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:46:49.752Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,751 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:46:49.759Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,758 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:46:49.781Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,781 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:46:49.785Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,781 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:46:49,781 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:46:49,781 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:46:49.881Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,880 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:46:49.883Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"4\",\r\n  \"timestamp\": \"2025-07-26T00:46:49.881269\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.4891174670937706,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"BULLISH\",\r\n      \"trend_strength\": 0.3495409584824366,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.27477047924121833,\r\n      \"price_action_score\": 0.1747704792412183\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3121,\r\n      \"signal_line\": 0.3121,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.01694915254237288,\r\n        \"strength\": 0.1694915254237288\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"BULLISH\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:46:49.883Z","level":"WARN","message":"Python stderr: 2025-07-26 00:46:49,880 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:46:49,881 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:46:49,881 - modules.decision_engine - INFO - Decision completed: HOLD with 0.49 confidence\r\n2025-07-26 00:46:49,881 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:46:49.962Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:46:49.962Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:46:49.969Z","level":"INFO","message":"Analysis completed for upload 4"}
{"timestamp":"2025-07-25T17:47:28.723Z","level":"INFO","message":"File uploaded successfully: chart_13d0833e-cd56-4a22-85e7-70aa972a0ebc.png","originalName":"image.png","size":314582,"mimetype":"image/png"}
{"timestamp":"2025-07-25T17:47:28.727Z","level":"INFO","message":"Upload record created with ID: 5"}
{"timestamp":"2025-07-25T17:47:28.741Z","level":"INFO","message":"Starting analysis for upload 5"}
{"timestamp":"2025-07-25T17:47:28.741Z","level":"INFO","message":"Executing Python analysis: D:\\Vicky\\project baru\\auto-analysis\\python_analysis\\main.py D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_13d0833e-cd56-4a22-85e7-70aa972a0ebc.png 5"}
{"timestamp":"2025-07-25T17:47:29.347Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:29,346 - __main__ - INFO - Starting analysis for image: D:\\Vicky\\project baru\\auto-analysis\\server\\uploads\\chart_13d0833e-cd56-4a22-85e7-70aa972a0ebc.png"}
{"timestamp":"2025-07-25T17:47:29.347Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:29,346 - __main__ - INFO - Step 1: Processing image..."}
{"timestamp":"2025-07-25T17:47:29.361Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:29,360 - modules.image_processor - INFO - Loaded image: (1000, 1912, 3)"}
{"timestamp":"2025-07-25T17:47:31.390Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,389 - __main__ - INFO - Step 2: Extracting OCR data..."}
{"timestamp":"2025-07-25T17:47:31.392Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,389 - modules.ocr_extractor - INFO - Starting OCR extraction..."}
{"timestamp":"2025-07-25T17:47:31.499Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,499 - modules.ocr_extractor - ERROR - Tesseract OCR failed: tesseract is not installed or it's not in your PATH. See README file for more information."}
{"timestamp":"2025-07-25T17:47:31.500Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,499 - modules.ocr_extractor - WARNING - No trading symbol found in text\r\n2025-07-26 00:47:31,499 - modules.ocr_extractor - WARNING - No timeframe found in text"}
{"timestamp":"2025-07-25T17:47:31.500Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,500 - modules.ocr_extractor - INFO - OCR extraction completed. Symbol: UNKNOWN, Timeframe: UNKNOWN"}
{"timestamp":"2025-07-25T17:47:31.502Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,501 - __main__ - INFO - Step 3: Analyzing price action..."}
{"timestamp":"2025-07-25T17:47:31.502Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,501 - modules.price_action_analyzer - INFO - Starting price action analysis..."}
{"timestamp":"2025-07-25T17:47:31.509Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,509 - modules.price_action_analyzer - INFO - Detected 0 candlestick patterns"}
{"timestamp":"2025-07-25T17:47:31.592Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,591 - modules.price_action_analyzer - INFO - Price action analysis completed. Trend: NEUTRAL\r\n2025-07-26 00:47:31,591 - __main__ - INFO - Step 4: Analyzing Smart Money Concepts..."}
{"timestamp":"2025-07-25T17:47:31.593Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,591 - modules.smc_analyzer - INFO - Starting SMC analysis..."}
{"timestamp":"2025-07-25T17:47:31.598Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,598 - modules.smc_analyzer - INFO - Detected 1 order blocks"}
{"timestamp":"2025-07-25T17:47:31.603Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,603 - modules.smc_analyzer - INFO - Identified 0 liquidity pools"}
{"timestamp":"2025-07-25T17:47:31.625Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,624 - modules.smc_analyzer - INFO - Identified 0 Fair Value Gaps"}
{"timestamp":"2025-07-25T17:47:31.626Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,625 - modules.smc_analyzer - INFO - SMC analysis completed. Structure: NEUTRAL\r\n2025-07-26 00:47:31,625 - __main__ - INFO - Step 5: Calculating technical indicators...\r\n2025-07-26 00:47:31,625 - modules.technical_indicators - INFO - Starting technical indicators calculation..."}
{"timestamp":"2025-07-25T17:47:31.702Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,701 - modules.technical_indicators - INFO - Technical indicators calculation completed"}
{"timestamp":"2025-07-25T17:47:31.705Z","level":"INFO","message":"Python stdout: {\r\n  \"success\": true,\r\n  \"upload_id\": \"5\",\r\n  \"timestamp\": \"2025-07-26T00:47:31.702267\",\r\n  \"ocr_data\": {\r\n    \"extracted_text\": \"\",\r\n    \"symbol\": \"UNKNOWN\",\r\n    \"timeframe\": \"UNKNOWN\",\r\n    \"current_price\": 0.0,\r\n    \"price_data\": {},\r\n    \"chart_metadata\": {}\r\n  },\r\n  \"analysis\": {\r\n    \"recommendation\": \"HOLD\",\r\n    \"confidence_score\": 0.46464960000000005,\r\n    \"entry_price\": 0.0,\r\n    \"stop_loss\": 0.0,\r\n    \"take_profit\": 0.0,\r\n    \"risk_reward_ratio\": 0.0,\r\n    \"price_action_signals\": {\r\n      \"candlestick_patterns\": [],\r\n      \"support_resistance\": {\r\n        \"all_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"key_levels\": [\r\n          {\r\n            \"y_position\": 0,\r\n            \"length\": 1912,\r\n            \"strength\": 1.0,\r\n            \"type\": \"resistance\"\r\n          }\r\n        ],\r\n        \"nearest_support\": null,\r\n        \"nearest_resistance\": {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        },\r\n        \"total_levels\": 1\r\n      },\r\n      \"trend\": \"NEUTRAL\",\r\n      \"trend_strength\": 0.0,\r\n      \"chart_patterns\": [\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1081.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.2162\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 3195.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.639\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 18505.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 1.0\r\n        },\r\n        {\r\n          \"type\": \"rectangle\",\r\n          \"area\": 1577.0,\r\n          \"vertices\": 4,\r\n          \"strength\": 0.3154\r\n        }\r\n      ],\r\n      \"signals\": [],\r\n      \"key_levels\": [\r\n        {\r\n          \"y_position\": 0,\r\n          \"length\": 1912,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"breakout_potential\": 0.1,\r\n      \"price_action_score\": 0.0\r\n    },\r\n    \"smc_signals\": {\r\n      \"order_blocks\": [\r\n        {\r\n          \"type\": \"bearish_ob\",\r\n          \"x\": 0,\r\n          \"y\": 0,\r\n          \"width\": 1912,\r\n          \"height\": 1000,\r\n          \"area\": 1909089.0,\r\n          \"strength\": 0.6912,\r\n          \"direction\": \"SELL\",\r\n          \"center\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"liquidity_pools\": [],\r\n      \"market_structure\": \"NEUTRAL\",\r\n      \"structure_strength\": 0.0,\r\n      \"breaker_blocks\": [],\r\n      \"fair_value_gaps\": [],\r\n      \"smc_signals\": [\r\n        {\r\n          \"type\": \"SELL\",\r\n          \"source\": \"order_block\",\r\n          \"strength\": 0.6912,\r\n          \"confidence\": 0.55296,\r\n          \"zone\": \"OB at 956, 500\",\r\n          \"category\": \"smc\"\r\n        }\r\n      ],\r\n      \"institutional_bias\": \"BEARISH\",\r\n      \"key_zones\": [\r\n        {\r\n          \"type\": \"order_block_zone\",\r\n          \"strength\": 0.6912,\r\n          \"location\": {\r\n            \"x\": 956,\r\n            \"y\": 500\r\n          }\r\n        }\r\n      ],\r\n      \"smc_score\": 0.04608\r\n    },\r\n    \"rsi_analysis\": {\r\n      \"value\": 100,\r\n      \"level\": \"extreme_overbought\",\r\n      \"signal\": \"SELL\",\r\n      \"strength\": 0.9,\r\n      \"detected\": false,\r\n      \"estimated\": true\r\n    },\r\n    \"macd_analysis\": {\r\n      \"macd_line\": 0.3135,\r\n      \"signal_line\": 0.3135,\r\n      \"histogram\": 0.0,\r\n      \"signal\": \"HOLD\",\r\n      \"strength\": 0.0,\r\n      \"detected\": true,\r\n      \"crossover\": true\r\n    },\r\n    \"moving_averages\": {\r\n      \"ma_20\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_50\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_100\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"ma_200\": {\r\n        \"detected\": true,\r\n        \"trend\": \"NEUTRAL\",\r\n        \"slope\": 0.0,\r\n        \"strength\": 0.0\r\n      },\r\n      \"alignment\": \"NEUTRAL\",\r\n      \"signal\": \"HOLD\"\r\n    },\r\n    \"support_resistance\": {\r\n      \"levels\": [\r\n        {\r\n          \"price_level\": 1000,\r\n          \"strength\": 1.0,\r\n          \"type\": \"resistance\"\r\n        }\r\n      ],\r\n      \"key_support\": null,\r\n      \"key_resistance\": {\r\n        \"price_level\": 1000,\r\n        \"strength\": 1.0,\r\n        \"type\": \"resistance\"\r\n      }\r\n    },\r\n    \"analysis_notes\": \"\",\r\n    \"market_structure\": \"NEUTRAL\",\r\n    \"trend_direction\": \"NEUTRAL\"\r\n  }\r\n}"}
{"timestamp":"2025-07-25T17:47:31.706Z","level":"WARN","message":"Python stderr: 2025-07-26 00:47:31,701 - __main__ - INFO - Step 6: Making trading decision...\r\n2025-07-26 00:47:31,702 - modules.decision_engine - INFO - Starting decision making process...\r\n2025-07-26 00:47:31,702 - modules.decision_engine - INFO - Decision completed: HOLD with 0.46 confidence\r\n2025-07-26 00:47:31,702 - __main__ - INFO - Analysis completed successfully"}
{"timestamp":"2025-07-25T17:47:31.798Z","level":"INFO","message":"Python process exited with code 0"}
{"timestamp":"2025-07-25T17:47:31.798Z","level":"INFO","message":"Python analysis completed successfully"}
{"timestamp":"2025-07-25T17:47:31.804Z","level":"INFO","message":"Analysis completed for upload 5"}
